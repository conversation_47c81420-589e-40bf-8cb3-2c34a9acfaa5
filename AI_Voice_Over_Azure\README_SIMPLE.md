# 🎬 Simple Azure AI Voice-Over Tool

## 📋 **Quick Setup**

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Configure Azure Services
Edit `config.py` with your Azure credentials:
```python
AZURE_SPEECH_KEY = "your_azure_speech_key"
AZURE_SPEECH_REGION = "eastus"
AZURE_STORAGE_ACCOUNT_NAME = "yourstorageaccount"
AZURE_STORAGE_ACCOUNT_KEY = "your_storage_key"

# Optional: For AI transcript polishing
AZURE_OPENAI_KEY = "your_azure_openai_key"
AZURE_OPENAI_ENDPOINT = "https://your-openai.openai.azure.com/"
```

## 🚀 **Usage**

### Basic Usage
```bash
# Simple processing with default voice (Aria)
python voice_processor.py video.mp4

# Choose specific voice
python voice_processor.py video.mp4 jenny

# Auto-approve (skip user review)
python voice_processor.py video.mp4 aria --auto
```

### Available Voices
- **aria**: Natural, friendly female voice
- **jenny**: Professional female voice  
- **guy**: Casual male voice
- **davis**: Professional male voice
- **jane**: Warm female voice

## 🔄 **What It Does**

1. **🎵 Audio Extraction**: Extracts audio and detects speech timing
2. **📝 Transcription**: Uses Azure Speech Services with word-level timestamps
3. **🤖 AI Polishing**: Improves grammar/spelling (if Azure OpenAI configured)
4. **✏️ User Review**: Interactive editing of transcript and timing
5. **🎤 Voice Generation**: Creates AI voice with perfect timing
6. **🎬 Video Assembly**: Combines original video with AI audio

## ✏️ **User Review Options**

During the review step, you can:
- **Approve and continue**: Use transcript as-is
- **Edit transcript text**: Fix any mistakes or improve content
- **Adjust global timing**: Shift all timing by X seconds
- **Show detailed view**: See word-level confidence scores

### Text Editing
- Fix spelling mistakes
- Improve grammar
- Add or remove content
- Correct AI polishing errors

### Timing Control
- Adjust when speech starts/ends
- Shift all timing globally
- Handle audio delays automatically

## 📁 **File Structure**

```
AI_Voice_Over_Azure/
├── voice_processor.py    # Main processing script
├── config.py            # Azure configuration
├── requirements.txt     # Dependencies
├── temp/               # Temporary audio files
└── output/             # Final AI voice-over videos
```

## 🎯 **Examples**

### Process a presentation video
```bash
python voice_processor.py presentation.mp4 davis
```

### Batch processing (auto-approve)
```bash
python voice_processor.py video1.mp4 aria --auto
python voice_processor.py video2.mp4 jenny --auto
```

### High-quality processing with review
```bash
python voice_processor.py important_video.mp4 jenny
# Review and edit transcript when prompted
# Adjust timing if needed
# Generate final video
```

## 🔧 **Features**

### ✅ **Timing Control**
- Automatic speech delay detection
- Word-level timestamp accuracy
- User-adjustable timing
- Perfect video synchronization

### ✅ **Quality Assurance**
- AI-powered transcript polishing
- Interactive review and editing
- Confidence score analysis
- Preview before final generation

### ✅ **Azure Integration**
- Enterprise-grade security
- High-quality neural voices
- Scalable processing
- Global CDN delivery

## 💰 **Cost Estimate**

Typical cost for 10-minute video:
- Azure Speech STT: ~$0.17
- Azure Speech TTS: ~$0.02
- Azure OpenAI polishing: ~$0.01
- Azure Storage: ~$0.01
- **Total: ~$0.21 per video**

## 🆘 **Troubleshooting**

### Common Issues:

**Azure Authentication Error**
- Check your Azure Speech key and region
- Verify Azure Storage credentials

**No Speech Recognized**
- Ensure video has clear audio
- Check for background noise
- Verify audio format compatibility

**Voice Generation Failed**
- Validate transcript was approved
- Check Azure Speech Services quota
- Verify voice name is correct

**Video Assembly Error**
- Ensure original video is accessible
- Check available disk space
- Verify video format support

## 🎉 **Success Tips**

1. **Use clear, high-quality source videos**
2. **Review transcripts for accuracy**
3. **Test different voices for best match**
4. **Adjust timing if speech starts late**
5. **Use auto-approve for consistent content**

---

**🎬 Ready to create amazing AI voice-over videos with perfect timing!**
