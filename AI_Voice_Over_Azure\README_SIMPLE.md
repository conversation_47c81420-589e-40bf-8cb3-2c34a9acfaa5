# 🎬 Azure AI Voice-Over Tool

## Setup
```bash
pip install -r requirements.txt
```

Edit `config.py`:
```python
AZURE_SPEECH_KEY = "your_key"
AZURE_SPEECH_REGION = "eastus"
AZURE_OPENAI_KEY = "your_openai_key"  # Optional
```

## Usage
```bash
# Basic
python voice_processor.py video.mp4

# Choose voice
python voice_processor.py video.mp4 jenny

# Auto-approve (skip review)
python voice_processor.py video.mp4 --auto
```

## Voices
- aria, jenny, guy, davis, jane

## Process
1. Extract audio + detect timing
2. Transcribe with timestamps
3. AI polish (optional)
4. User review/edit
5. Generate AI voice
6. Assemble final video

## Review Options
- Approve and continue
- Edit transcript text
- Adjust timing

## Files
- `voice_processor.py` - Main script
- `temp/` - Temporary files
- `output/` - Final videos

## Cost
~$0.21 per 10-minute video
