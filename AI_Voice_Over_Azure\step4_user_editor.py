#!/usr/bin/env python3
"""
Step 4: User Review & Content Editor
Interactive interface for reviewing and editing transcript with timing control
"""

import os
import sys
import json
import logging
from typing import Optional, Dict, List
from datetime import datetime
import copy

# Import configuration
from config import *

class UserEditor:
    def __init__(self):
        """Initialize the User Editor"""
        self.setup_logging()
        
    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('step4_user_editing.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)

    def load_polished_data(self, processing_file: str) -> Dict:
        """Load polished data from Step 3"""
        try:
            with open(processing_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.info(f"Loaded polished data: {data['processing_id']}")
            return data
            
        except Exception as e:
            self.logger.error(f"Error loading polished data: {str(e)}")
            raise

    def display_timeline_overview(self, transcript_data: Dict):
        """Display timeline overview to user"""
        segments = transcript_data["segments"]
        timing_analysis = transcript_data.get("timing_analysis", {})
        
        print("\n" + "="*80)
        print("⏰ TIMELINE OVERVIEW")
        print("="*80)
        
        # Show timing summary
        speech_delay = timing_analysis.get("speech_start_delay", 0)
        total_duration = timing_analysis.get("audio_duration", 0)
        
        print(f"🎬 Video duration: {total_duration:.1f} seconds")
        print(f"🗣️ Speech starts at: {speech_delay:.1f} seconds")
        print(f"📊 Total segments: {len(segments)}")
        
        # Visual timeline
        print(f"\n📈 Timeline:")
        timeline = f"[{'-'*int(speech_delay)}|"
        for i, segment in enumerate(segments[:5]):  # Show first 5 segments
            duration = segment["end_time"] - segment["start_time"]
            timeline += f"S{i+1}({duration:.1f}s)|"
        timeline += "...]"
        print(f"   {timeline}")
        print(f"   0s{' '*int(speech_delay*2)}Speech starts")

    def display_segment_editor(self, segment: Dict, segment_num: int, total_segments: int):
        """Display individual segment editor"""
        print(f"\n" + "="*80)
        print(f"✏️ SEGMENT {segment_num}/{total_segments} EDITOR")
        print("="*80)
        
        # Segment info
        print(f"⏰ Timing: {segment['start_time']:.1f}s - {segment['end_time']:.1f}s (Duration: {segment['end_time'] - segment['start_time']:.1f}s)")
        print(f"⭐ Confidence: {segment['confidence']:.2f}")
        
        # Show AI changes if any
        if segment.get("ai_polished") and segment.get("original_text"):
            print(f"\n🤖 AI CHANGES:")
            print(f"   Original: \"{segment['original_text']}\"")
            print(f"   Polished: \"{segment['text']}\"")
            
            changes = segment.get("polishing_info", {}).get("changes_made", [])
            if changes:
                print(f"   Changes: {', '.join([c['description'] for c in changes])}")
        
        # Current text
        print(f"\n📝 CURRENT TEXT:")
        print(f"   \"{segment['text']}\"")
        
        # Low confidence words
        low_conf_words = [w for w in segment.get("words", []) if w.get("confidence", 1.0) < 0.8]
        if low_conf_words:
            print(f"\n⚠️ LOW CONFIDENCE WORDS:")
            for word in low_conf_words:
                print(f"   • '{word['word']}' (confidence: {word['confidence']:.2f})")

    def get_user_choice(self, prompt: str, choices: List[str]) -> str:
        """Get user choice from list of options"""
        while True:
            print(f"\n{prompt}")
            for i, choice in enumerate(choices, 1):
                print(f"   {i}. {choice}")
            
            try:
                choice_num = int(input("\nEnter choice number: "))
                if 1 <= choice_num <= len(choices):
                    return choices[choice_num - 1]
                else:
                    print("Invalid choice. Please try again.")
            except ValueError:
                print("Please enter a valid number.")

    def edit_segment_text(self, segment: Dict) -> Dict:
        """Allow user to edit segment text"""
        print(f"\n✏️ EDIT TEXT:")
        print(f"Current: \"{segment['text']}\"")
        
        new_text = input("Enter new text (or press Enter to keep current): ").strip()
        
        if new_text and new_text != segment['text']:
            # Track the change
            segment['user_edited'] = True
            segment['user_changes'] = segment.get('user_changes', [])
            segment['user_changes'].append({
                "type": "text_edit",
                "original": segment['text'],
                "new": new_text,
                "timestamp": datetime.now().isoformat()
            })
            segment['text'] = new_text
            print("✅ Text updated!")
        else:
            print("📝 Text unchanged")
        
        return segment

    def adjust_segment_timing(self, segment: Dict) -> Dict:
        """Allow user to adjust segment timing"""
        print(f"\n⏰ ADJUST TIMING:")
        print(f"Current: {segment['start_time']:.1f}s - {segment['end_time']:.1f}s")
        
        try:
            new_start = input(f"New start time (current: {segment['start_time']:.1f}s): ").strip()
            new_end = input(f"New end time (current: {segment['end_time']:.1f}s): ").strip()
            
            if new_start:
                new_start_time = float(new_start)
                if new_start_time != segment['start_time']:
                    segment['original_start_time'] = segment.get('original_start_time', segment['start_time'])
                    segment['start_time'] = new_start_time
                    segment['timing_adjusted'] = True
                    print(f"✅ Start time updated to {new_start_time:.1f}s")
            
            if new_end:
                new_end_time = float(new_end)
                if new_end_time != segment['end_time']:
                    segment['original_end_time'] = segment.get('original_end_time', segment['end_time'])
                    segment['end_time'] = new_end_time
                    segment['timing_adjusted'] = True
                    print(f"✅ End time updated to {new_end_time:.1f}s")
            
            # Validate timing
            if segment['start_time'] >= segment['end_time']:
                print("⚠️ Warning: Start time should be less than end time")
                
        except ValueError:
            print("❌ Invalid time format. Please enter numbers only.")
        
        return segment

    def preview_audio_segment(self, segment: Dict, audio_path: str):
        """Preview audio segment (placeholder for actual audio playback)"""
        print(f"\n🎵 AUDIO PREVIEW:")
        print(f"   Segment: {segment['start_time']:.1f}s - {segment['end_time']:.1f}s")
        print(f"   Text: \"{segment['text']}\"")
        print("   [Audio playback would happen here in full implementation]")
        
        # In a full implementation, this would:
        # 1. Extract audio segment from the original file
        # 2. Play it using a library like pygame or playsound
        # 3. Allow user to hear what they're editing

    def review_segment(self, segment: Dict, segment_num: int, total_segments: int, audio_path: str) -> Dict:
        """Review and edit a single segment"""
        while True:
            self.display_segment_editor(segment, segment_num, total_segments)
            
            choices = [
                "Keep as is (continue to next)",
                "Edit text",
                "Adjust timing",
                "Preview audio",
                "Show word details",
                "Go back to previous segment"
            ]
            
            choice = self.get_user_choice("What would you like to do?", choices)
            
            if choice == choices[0]:  # Keep as is
                break
            elif choice == choices[1]:  # Edit text
                segment = self.edit_segment_text(segment)
            elif choice == choices[2]:  # Adjust timing
                segment = self.adjust_segment_timing(segment)
            elif choice == choices[3]:  # Preview audio
                self.preview_audio_segment(segment, audio_path)
            elif choice == choices[4]:  # Show word details
                self.show_word_details(segment)
            elif choice == choices[5]:  # Go back
                return segment, "back"
        
        return segment, "continue"

    def show_word_details(self, segment: Dict):
        """Show detailed word-level information"""
        print(f"\n📝 WORD-LEVEL DETAILS:")
        words = segment.get("words", [])
        
        if not words:
            print("   No word-level data available")
            return
        
        for word in words:
            confidence_icon = "✅" if word.get("confidence", 1.0) > 0.8 else "⚠️"
            print(f"   {confidence_icon} '{word['word']}' [{word['start_time']:.1f}s-{word['end_time']:.1f}s] (conf: {word['confidence']:.2f})")

    def global_timing_adjustment(self, transcript_data: Dict) -> Dict:
        """Allow global timing adjustments"""
        print(f"\n⏰ GLOBAL TIMING ADJUSTMENT:")
        print("Adjust timing for all segments at once")
        
        try:
            shift = input("Shift all timing by how many seconds? (e.g., +2.0, -1.5): ").strip()
            
            if shift:
                shift_amount = float(shift)
                
                for segment in transcript_data["segments"]:
                    segment["start_time"] += shift_amount
                    segment["end_time"] += shift_amount
                    
                    # Also adjust word-level timing
                    for word in segment.get("words", []):
                        word["start_time"] += shift_amount
                        word["end_time"] += shift_amount
                
                print(f"✅ All timing shifted by {shift_amount:.1f} seconds")
                
                # Update timing analysis
                if "timing_analysis" in transcript_data:
                    transcript_data["timing_analysis"]["speech_start_delay"] += shift_amount
                
        except ValueError:
            print("❌ Invalid shift amount. Please enter a number.")
        
        return transcript_data

    def process_user_review(self, processing_file: str) -> Optional[str]:
        """Main method for user review and editing"""
        try:
            self.logger.info("=== Step 4: User Review & Content Editing ===")
            
            # Step 4.1: Load polished data
            self.logger.info("📂 Loading polished data...")
            polished_data = self.load_polished_data(processing_file)
            
            transcript_data = polished_data["transcript_data"]
            audio_path = polished_data["files"]["audio_file"]
            
            # Step 4.2: Display overview
            self.display_timeline_overview(transcript_data)
            
            # Check if auto-approval is enabled for high confidence
            avg_confidence = transcript_data["statistics"]["average_confidence"]
            if (AUTO_APPROVE_HIGH_CONFIDENCE and 
                avg_confidence > HIGH_CONFIDENCE_THRESHOLD and
                len(polished_data.get("review_priorities", [])) == 0):
                
                print(f"\n🎯 HIGH QUALITY DETECTED!")
                print(f"   Average confidence: {avg_confidence:.2f}")
                print(f"   No review priorities found")
                
                auto_approve = input("\nAuto-approve this transcript? (y/n): ").lower().strip()
                if auto_approve == 'y':
                    self.logger.info("User chose auto-approval for high-quality transcript")
                    return self.save_final_transcript(polished_data, auto_approved=True)
            
            # Step 4.3: Review segments
            print(f"\n📝 Starting segment-by-segment review...")
            
            segments = transcript_data["segments"]
            current_segment = 0
            
            while current_segment < len(segments):
                segment, action = self.review_segment(
                    segments[current_segment], 
                    current_segment + 1, 
                    len(segments),
                    audio_path
                )
                
                segments[current_segment] = segment
                
                if action == "back" and current_segment > 0:
                    current_segment -= 1
                else:
                    current_segment += 1
            
            # Step 4.4: Global adjustments
            print(f"\n🌐 GLOBAL ADJUSTMENTS:")
            global_choice = self.get_user_choice(
                "Would you like to make global timing adjustments?",
                ["No, continue to final approval", "Yes, adjust global timing"]
            )
            
            if global_choice.startswith("Yes"):
                transcript_data = self.global_timing_adjustment(transcript_data)
            
            # Step 4.5: Final approval
            print(f"\n✅ FINAL APPROVAL:")
            print("Review complete! Ready to generate AI voice.")
            
            final_choice = self.get_user_choice(
                "What would you like to do?",
                ["Approve and generate AI voice", "Review segments again", "Save and exit"]
            )
            
            if final_choice.startswith("Approve"):
                return self.save_final_transcript(polished_data)
            elif final_choice.startswith("Review"):
                return self.process_user_review(processing_file)  # Start over
            else:
                return self.save_final_transcript(polished_data, approved=False)
            
        except Exception as e:
            self.logger.error(f"Error in user review process: {str(e)}")
            return None

    def save_final_transcript(self, polished_data: Dict, approved: bool = True, auto_approved: bool = False) -> str:
        """Save final transcript after user review"""
        try:
            # Update processing data
            polished_data["step"] = "4_user_review"
            polished_data["status"] = "completed" if approved else "saved"
            polished_data["updated_at"] = datetime.now().isoformat()
            polished_data["user_approved"] = approved
            polished_data["auto_approved"] = auto_approved
            polished_data["next_step"] = "5_voice_generation" if approved else "4_user_review"
            
            # Add user review metadata
            polished_data["user_review"] = {
                "completed": True,
                "approved": approved,
                "auto_approved": auto_approved,
                "review_time": datetime.now().isoformat()
            }
            
            # Save final data
            processing_id = polished_data["processing_id"]
            output_file = f"{PROCESSING_FOLDER}/{processing_id}_step4.json"
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(polished_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Final transcript saved: {output_file}")
            return output_file
            
        except Exception as e:
            self.logger.error(f"Error saving final transcript: {str(e)}")
            raise

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python step4_user_editor.py <processing_file>")
        print("Example: python step4_user_editor.py processing/proc_123_step3.json")
        sys.exit(1)
    
    processing_file = sys.argv[1]
    
    if not os.path.exists(processing_file):
        print(f"Error: Processing file not found: {processing_file}")
        sys.exit(1)
    
    editor = UserEditor()
    result = editor.process_user_review(processing_file)
    
    if result:
        print(f"\n🎯 SUCCESS! Final transcript saved to: {result}")
        print("🎤 Next: python step5_voice_generator.py <final_file>")
    else:
        print("\n❌ FAILED! Check logs for details")
        sys.exit(1)
