#!/usr/bin/env python3
"""
Step 5: Timing-Aware AI Voice Generation
Generates AI voice using Azure Speech Services with perfect timing synchronization
"""

import os
import sys
import json
import logging
import time
from typing import Optional, Dict, List
import azure.cognitiveservices.speech as speechsdk
from datetime import datetime
import tempfile

# Import configuration
from config import *

class TimedVoiceGenerator:
    def __init__(self):
        """Initialize the Timed Voice Generator"""
        self.setup_logging()
        self.setup_azure_speech()
        
    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('step5_voice_generation.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_azure_speech(self):
        """Setup Azure Speech Services for TTS"""
        try:
            if not AZURE_SPEECH_KEY or AZURE_SPEECH_KEY == "your_azure_speech_key_here":
                raise ValueError("Azure Speech key not configured")

            self.speech_config = speechsdk.SpeechConfig(
                subscription=AZURE_SPEECH_KEY,
                region=AZURE_SPEECH_REGION
            )
            
            self.logger.info("Azure Speech Services (TTS) initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Error setting up Azure Speech: {str(e)}")
            raise

    def load_final_transcript(self, processing_file: str) -> Dict:
        """Load final transcript data from Step 4"""
        try:
            with open(processing_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if not data.get("user_approved") and not data.get("auto_approved"):
                raise ValueError("Transcript not approved for voice generation")
            
            self.logger.info(f"Loaded approved transcript: {data['processing_id']}")
            return data
            
        except Exception as e:
            self.logger.error(f"Error loading final transcript: {str(e)}")
            raise

    def create_timing_structure(self, transcript_data: Dict) -> Dict:
        """Create detailed timing structure for voice generation"""
        try:
            segments = transcript_data["segments"]
            timing_analysis = transcript_data.get("timing_analysis", {})
            
            # Calculate total duration needed
            last_segment = segments[-1] if segments else None
            total_duration = last_segment["end_time"] if last_segment else 0
            
            timing_structure = {
                "total_duration": total_duration,
                "speech_start_delay": timing_analysis.get("speech_start_delay", 0),
                "segments": [],
                "silence_periods": []
            }
            
            # Process each segment
            for i, segment in enumerate(segments):
                segment_timing = {
                    "id": segment["id"],
                    "text": segment["text"],
                    "start_time": segment["start_time"],
                    "end_time": segment["end_time"],
                    "duration": segment["end_time"] - segment["start_time"],
                    "silence_before": 0,
                    "silence_after": 0
                }
                
                # Calculate silence before this segment
                if i == 0:
                    # First segment - silence from start
                    segment_timing["silence_before"] = segment["start_time"]
                else:
                    # Silence between previous segment and this one
                    prev_segment = segments[i-1]
                    gap = segment["start_time"] - prev_segment["end_time"]
                    segment_timing["silence_before"] = max(0, gap)
                
                # Calculate silence after last segment
                if i == len(segments) - 1:
                    # Last segment - might need trailing silence
                    segment_timing["silence_after"] = 0  # Will be calculated if needed
                
                timing_structure["segments"].append(segment_timing)
            
            self.logger.info(f"Timing structure created: {len(timing_structure['segments'])} segments, "
                           f"{timing_structure['total_duration']:.1f}s total duration")
            return timing_structure
            
        except Exception as e:
            self.logger.error(f"Error creating timing structure: {str(e)}")
            raise

    def generate_silence_audio(self, duration_seconds: float, output_path: str) -> bool:
        """Generate silence audio file"""
        try:
            if duration_seconds <= 0:
                return True
            
            self.logger.info(f"Generating {duration_seconds:.1f}s of silence...")
            
            # Use Azure Speech to generate silence (or create programmatically)
            # For now, we'll create a simple approach
            
            # Create silence using Azure TTS with empty SSML
            silence_ssml = f"""
            <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="en-US">
                <voice name="{AZURE_SPEECH_TTS_CONFIG['voice_name']}">
                    <break time="{duration_seconds}s"/>
                </voice>
            </speak>
            """
            
            # Configure audio output
            audio_config = speechsdk.audio.AudioOutputConfig(filename=output_path)
            
            # Create synthesizer
            synthesizer = speechsdk.SpeechSynthesizer(
                speech_config=self.speech_config,
                audio_config=audio_config
            )
            
            # Generate silence
            result = synthesizer.speak_ssml_async(silence_ssml).get()
            
            if result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
                self.logger.info(f"Silence generated: {output_path}")
                return True
            else:
                self.logger.error(f"Failed to generate silence: {result.reason}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error generating silence: {str(e)}")
            return False

    def generate_segment_audio(self, segment_text: str, voice_name: str, output_path: str) -> bool:
        """Generate audio for a single segment"""
        try:
            self.logger.info(f"Generating audio for: '{segment_text[:50]}...'")
            
            # Configure voice
            self.speech_config.speech_synthesis_voice_name = voice_name
            
            # Create audio configuration
            audio_config = speechsdk.audio.AudioOutputConfig(filename=output_path)
            
            # Create synthesizer
            synthesizer = speechsdk.SpeechSynthesizer(
                speech_config=self.speech_config,
                audio_config=audio_config
            )
            
            # Generate speech
            result = synthesizer.speak_text_async(segment_text).get()
            
            if result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
                self.logger.info(f"Segment audio generated: {output_path}")
                return True
            elif result.reason == speechsdk.ResultReason.Canceled:
                cancellation_details = result.cancellation_details
                self.logger.error(f"Speech synthesis canceled: {cancellation_details.reason}")
                if cancellation_details.error_details:
                    self.logger.error(f"Error details: {cancellation_details.error_details}")
                return False
            else:
                self.logger.error(f"Speech synthesis failed: {result.reason}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error generating segment audio: {str(e)}")
            return False

    def combine_audio_segments(self, audio_files: List[str], output_path: str) -> bool:
        """Combine multiple audio files into one with perfect timing"""
        try:
            self.logger.info(f"Combining {len(audio_files)} audio segments...")
            
            # Use pydub for audio manipulation if available
            try:
                from pydub import AudioSegment
                
                combined = AudioSegment.empty()
                
                for audio_file in audio_files:
                    if os.path.exists(audio_file):
                        segment = AudioSegment.from_file(audio_file)
                        combined += segment
                        self.logger.debug(f"Added segment: {audio_file}")
                    else:
                        self.logger.warning(f"Audio file not found: {audio_file}")
                
                # Export combined audio
                combined.export(output_path, format="mp3")
                self.logger.info(f"Combined audio saved: {output_path}")
                return True
                
            except ImportError:
                self.logger.error("pydub not available for audio combination")
                return False
                
        except Exception as e:
            self.logger.error(f"Error combining audio segments: {str(e)}")
            return False

    def generate_timed_voice(self, timing_structure: Dict, voice_name: str) -> Optional[str]:
        """Generate AI voice with perfect timing"""
        try:
            self.logger.info("Starting timed AI voice generation...")
            
            # Create temporary directory for audio segments
            temp_dir = tempfile.mkdtemp()
            audio_segments = []
            
            # Generate each segment with proper timing
            for i, segment_timing in enumerate(timing_structure["segments"]):
                segment_id = segment_timing["id"]
                
                # Generate silence before segment if needed
                if segment_timing["silence_before"] > 0:
                    silence_file = f"{temp_dir}/silence_before_{segment_id}.mp3"
                    if self.generate_silence_audio(segment_timing["silence_before"], silence_file):
                        audio_segments.append(silence_file)
                
                # Generate speech for segment
                speech_file = f"{temp_dir}/speech_{segment_id}.mp3"
                if self.generate_segment_audio(segment_timing["text"], voice_name, speech_file):
                    audio_segments.append(speech_file)
                else:
                    self.logger.error(f"Failed to generate audio for segment {segment_id}")
                    return None
                
                self.logger.info(f"Processed segment {i+1}/{len(timing_structure['segments'])}")
            
            # Combine all audio segments
            timestamp = int(time.time())
            output_filename = f"ai_voice_{timestamp}.mp3"
            output_path = os.path.join(TEMP_FOLDER, output_filename)
            
            if self.combine_audio_segments(audio_segments, output_path):
                # Cleanup temporary files
                for temp_file in audio_segments:
                    try:
                        os.remove(temp_file)
                    except:
                        pass
                
                try:
                    os.rmdir(temp_dir)
                except:
                    pass
                
                self.logger.info(f"Timed AI voice generated successfully: {output_path}")
                return output_path
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"Error generating timed voice: {str(e)}")
            return None

    def validate_voice_timing(self, voice_file: str, timing_structure: Dict) -> Dict:
        """Validate that generated voice matches expected timing"""
        try:
            self.logger.info("Validating voice timing...")
            
            # Get actual audio duration
            try:
                from pydub import AudioSegment
                audio = AudioSegment.from_file(voice_file)
                actual_duration = len(audio) / 1000.0  # Convert to seconds
            except ImportError:
                # Fallback: estimate from file size
                file_size = os.path.getsize(voice_file)
                actual_duration = file_size / (128 * 1024 / 8)  # Rough estimate for 128kbps MP3
            
            expected_duration = timing_structure["total_duration"]
            
            validation_result = {
                "expected_duration": expected_duration,
                "actual_duration": actual_duration,
                "duration_difference": abs(actual_duration - expected_duration),
                "timing_accuracy": "good" if abs(actual_duration - expected_duration) < 1.0 else "needs_review",
                "validation_time": datetime.now().isoformat()
            }
            
            self.logger.info(f"Timing validation: expected {expected_duration:.1f}s, "
                           f"actual {actual_duration:.1f}s, "
                           f"difference {validation_result['duration_difference']:.1f}s")
            
            return validation_result
            
        except Exception as e:
            self.logger.error(f"Error validating voice timing: {str(e)}")
            return {
                "validation_error": str(e),
                "timing_accuracy": "unknown"
            }

    def save_voice_data(self, final_data: Dict, voice_file: str, timing_structure: Dict, 
                       validation_result: Dict) -> str:
        """Save voice generation data"""
        try:
            # Update processing data
            final_data["step"] = "5_voice_generation"
            final_data["status"] = "completed"
            final_data["updated_at"] = datetime.now().isoformat()
            final_data["next_step"] = "6_video_assembly"
            
            # Add voice generation data
            final_data["voice_generation"] = {
                "voice_file": voice_file,
                "voice_name": AZURE_VOICE_OPTIONS.get("aria", "en-US-AriaNeural"),  # Default voice
                "timing_structure": timing_structure,
                "validation_result": validation_result,
                "generation_time": datetime.now().isoformat()
            }
            
            # Save data
            processing_id = final_data["processing_id"]
            output_file = f"{PROCESSING_FOLDER}/{processing_id}_step5.json"
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(final_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Voice generation data saved: {output_file}")
            return output_file
            
        except Exception as e:
            self.logger.error(f"Error saving voice data: {str(e)}")
            raise

    def process_voice_generation(self, processing_file: str, voice_choice: str = "aria") -> Optional[str]:
        """Main method to generate AI voice with timing"""
        try:
            self.logger.info("=== Step 5: Timing-Aware AI Voice Generation ===")
            
            # Step 5.1: Load final transcript
            self.logger.info("📂 Loading approved transcript...")
            final_data = self.load_final_transcript(processing_file)
            
            transcript_data = final_data["transcript_data"]
            
            # Step 5.2: Create timing structure
            self.logger.info("⏰ Creating timing structure...")
            timing_structure = self.create_timing_structure(transcript_data)
            
            # Step 5.3: Generate AI voice with timing
            voice_name = AZURE_VOICE_OPTIONS.get(voice_choice, AZURE_SPEECH_TTS_CONFIG["voice_name"])
            self.logger.info(f"🎤 Generating AI voice with {voice_name}...")
            
            voice_file = self.generate_timed_voice(timing_structure, voice_name)
            if not voice_file:
                return None
            
            # Step 5.4: Validate timing
            self.logger.info("🔍 Validating voice timing...")
            validation_result = self.validate_voice_timing(voice_file, timing_structure)
            
            # Step 5.5: Save voice data
            self.logger.info("💾 Saving voice generation data...")
            output_file = self.save_voice_data(final_data, voice_file, timing_structure, validation_result)
            
            # Display results
            self.display_results(voice_file, timing_structure, validation_result)
            
            self.logger.info("✅ Step 5 completed successfully!")
            self.logger.info(f"📁 Voice data: {output_file}")
            self.logger.info("🎬 Next step: Video assembly")
            
            return output_file
            
        except Exception as e:
            self.logger.error(f"Error in voice generation process: {str(e)}")
            return None

    def display_results(self, voice_file: str, timing_structure: Dict, validation_result: Dict):
        """Display voice generation results"""
        print("\n" + "="*60)
        print("🎤 STEP 5 RESULTS: AI Voice Generation with Timing")
        print("="*60)
        print(f"🎵 Voice file: {voice_file}")
        print(f"📊 Total segments: {len(timing_structure['segments'])}")
        print(f"⏱️ Expected duration: {timing_structure['total_duration']:.1f}s")
        print(f"🎯 Actual duration: {validation_result.get('actual_duration', 'Unknown'):.1f}s")
        print(f"📈 Timing accuracy: {validation_result.get('timing_accuracy', 'Unknown')}")
        
        if validation_result.get('duration_difference', 0) > 1.0:
            print(f"⚠️ Warning: Timing difference of {validation_result['duration_difference']:.1f}s detected")
        else:
            print("✅ Timing validation passed")
        
        print("\n✅ Ready for Step 6: Video Assembly")
        print("="*60)

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python step5_voice_generator.py <processing_file> [voice_choice]")
        print("Example: python step5_voice_generator.py processing/proc_123_step4.json aria")
        print(f"Available voices: {', '.join(AZURE_VOICE_OPTIONS.keys())}")
        sys.exit(1)
    
    processing_file = sys.argv[1]
    voice_choice = sys.argv[2] if len(sys.argv) > 2 else "aria"
    
    if not os.path.exists(processing_file):
        print(f"Error: Processing file not found: {processing_file}")
        sys.exit(1)
    
    if voice_choice not in AZURE_VOICE_OPTIONS:
        print(f"Error: Invalid voice choice. Available: {', '.join(AZURE_VOICE_OPTIONS.keys())}")
        sys.exit(1)
    
    generator = TimedVoiceGenerator()
    result = generator.process_voice_generation(processing_file, voice_choice)
    
    if result:
        print(f"\n🎯 SUCCESS! Voice generation data saved to: {result}")
        print("🎬 Next: python step6_video_assembler.py <voice_file>")
    else:
        print("\n❌ FAILED! Check logs for details")
        sys.exit(1)
