#!/usr/bin/env python3
"""
Azure AI Voice-Over Processor
Simplified single-file solution for AI voice-over with timing control
"""

import os
import sys
import json
import time
import uuid
from typing import Optional, Dict, List
from datetime import datetime
import azure.cognitiveservices.speech as speechsdk
from moviepy.editor import VideoFileClip, AudioFileClip
from config import *

class VoiceProcessor:
    def __init__(self):
        """Initialize the Voice Processor"""
        self.setup_azure()
        create_directories()
        
    def setup_azure(self):
        """Setup Azure services"""
        # Azure Speech Services
        self.speech_config = speechsdk.SpeechConfig(
            subscription=AZURE_SPEECH_KEY,
            region=AZURE_SPEECH_REGION
        )
        self.speech_config.request_word_level_timestamps()
        self.speech_config.output_format = speechsdk.OutputFormat.Detailed
        self.speech_config.speech_recognition_language = "en-US"
        
        # Azure OpenAI (if available)
        self.openai_client = None
        if ENABLE_AI_POLISHING:
            try:
                from openai import AzureOpenAI
                if AZURE_OPENAI_KEY and AZURE_OPENAI_KEY != "your_azure_openai_key_here":
                    self.openai_client = AzureOpenAI(
                        api_key=AZURE_OPENAI_KEY,
                        api_version=AZURE_OPENAI_API_VERSION,
                        azure_endpoint=AZURE_OPENAI_ENDPOINT
                    )
                    print("✅ Azure OpenAI initialized for transcript polishing")
            except ImportError:
                print("⚠️ Azure OpenAI not available - skipping AI polishing")

    def extract_audio_with_timing(self, video_path: str) -> Dict:
        """Extract audio and analyze timing"""
        print("🎵 Extracting audio and analyzing timing...")
        
        # Generate unique ID
        unique_id = f"{int(time.time())}_{str(uuid.uuid4())[:8]}"
        audio_path = f"{TEMP_FOLDER}/audio_{unique_id}.wav"
        
        # Extract audio
        video = VideoFileClip(video_path)
        if video.audio is None:
            raise ValueError("Video has no audio track")
        
        # Get metadata
        metadata = {
            "filename": os.path.basename(video_path),
            "duration": video.duration,
            "fps": video.fps,
            "size": video.size
        }
        
        # Extract audio optimized for Azure Speech
        audio = video.audio
        audio.write_audiofile(
            audio_path,
            verbose=False,
            logger=None,
            codec='pcm_s16le',
            ffmpeg_params=['-ar', '16000', '-ac', '1']
        )
        
        video.close()
        audio.close()
        
        # Detect speech timing
        speech_delay = self.detect_speech_delay(audio_path)
        
        result = {
            "unique_id": unique_id,
            "audio_path": audio_path,
            "video_metadata": metadata,
            "speech_delay": speech_delay
        }
        
        print(f"✅ Audio extracted - Speech starts at {speech_delay:.1f}s")
        return result

    def detect_speech_delay(self, audio_path: str) -> float:
        """Detect when speech actually starts"""
        try:
            from pydub import AudioSegment
            from pydub.silence import detect_leading_silence
            
            audio = AudioSegment.from_wav(audio_path)
            silence_ms = detect_leading_silence(audio, silence_threshold=-40.0, chunk_size=10)
            return silence_ms / 1000.0
        except ImportError:
            # Fallback estimation
            file_size = os.path.getsize(audio_path)
            return 2.0 if file_size < 5 * 1024 * 1024 else 3.0

    def transcribe_with_timing(self, audio_data: Dict) -> Dict:
        """Transcribe audio with word-level timestamps"""
        print("📝 Transcribing with Azure Speech Services...")
        
        audio_path = audio_data["audio_path"]
        speech_delay = audio_data["speech_delay"]
        
        # Create audio configuration
        audio_config = speechsdk.audio.AudioConfig(filename=os.path.abspath(audio_path))
        
        # Create speech recognizer
        speech_recognizer = speechsdk.SpeechRecognizer(
            speech_config=self.speech_config,
            audio_config=audio_config
        )
        
        # Transcription data
        segments = []
        segment_id = 1
        done = False
        
        def recognized_cb(evt):
            nonlocal segment_id
            if evt.result.reason == speechsdk.ResultReason.RecognizedSpeech:
                try:
                    detailed_result = json.loads(evt.result.json)
                    if 'NBest' in detailed_result and detailed_result['NBest']:
                        best_result = detailed_result['NBest'][0]
                        
                        # Create segment with adjusted timing
                        segment = {
                            "id": segment_id,
                            "text": best_result.get('Display', ''),
                            "start_time": (evt.result.offset / 10000000.0) + speech_delay,
                            "duration": evt.result.duration / 10000000.0,
                            "confidence": best_result.get('Confidence', 0.0),
                            "words": []
                        }
                        
                        # Add word-level timing
                        if 'Words' in best_result:
                            for word_info in best_result['Words']:
                                word_data = {
                                    "word": word_info.get('Word', ''),
                                    "start_time": (word_info.get('Offset', 0) / 10000000.0) + speech_delay,
                                    "duration": word_info.get('Duration', 0) / 10000000.0,
                                    "confidence": word_info.get('Confidence', 0.0)
                                }
                                word_data["end_time"] = word_data["start_time"] + word_data["duration"]
                                segment["words"].append(word_data)
                        
                        segment["end_time"] = segment["start_time"] + segment["duration"]
                        segments.append(segment)
                        segment_id += 1
                        
                except Exception as e:
                    print(f"⚠️ Error processing segment: {e}")

        def stop_cb(evt):
            nonlocal done
            done = True

        # Connect callbacks and start recognition
        speech_recognizer.recognized.connect(recognized_cb)
        speech_recognizer.session_stopped.connect(stop_cb)
        speech_recognizer.canceled.connect(stop_cb)
        
        speech_recognizer.start_continuous_recognition()
        
        # Wait for completion
        start_time = time.time()
        while not done and (time.time() - start_time) < MAX_WAIT_TIME:
            time.sleep(0.5)
        
        speech_recognizer.stop_continuous_recognition()
        
        if not segments:
            raise ValueError("No speech recognized in audio")
        
        # Calculate statistics
        all_confidences = [seg["confidence"] for seg in segments]
        avg_confidence = sum(all_confidences) / len(all_confidences)
        
        transcript_data = {
            "segments": segments,
            "statistics": {
                "total_segments": len(segments),
                "total_words": sum(len(seg["words"]) for seg in segments),
                "average_confidence": avg_confidence
            }
        }
        
        audio_data["transcript"] = transcript_data
        print(f"✅ Transcription complete - {len(segments)} segments, confidence: {avg_confidence:.2f}")
        return audio_data

    def polish_transcript(self, data: Dict) -> Dict:
        """Polish transcript using AI"""
        if not self.openai_client:
            print("⚠️ Skipping AI polishing - Azure OpenAI not available")
            return data
        
        print("🤖 Polishing transcript with Azure OpenAI...")
        
        segments = data["transcript"]["segments"]
        polished_count = 0
        
        for segment in segments:
            original_text = segment["text"]
            
            try:
                # Polish with Azure OpenAI
                prompt = f"""Fix grammar and spelling in this transcript segment while preserving meaning:
                
Original: "{original_text}"

Rules:
- Fix obvious spelling mistakes
- Improve grammar naturally
- Remove excessive filler words (um, uh)
- Keep technical terms unchanged
- Don't add new information

Provide only the improved text:"""

                response = self.openai_client.chat.completions.create(
                    model=AZURE_OPENAI_DEPLOYMENT,
                    messages=[
                        {"role": "system", "content": "You are a professional transcript editor."},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=200,
                    temperature=0.3
                )
                
                polished_text = response.choices[0].message.content.strip()
                
                if polished_text != original_text:
                    segment["original_text"] = original_text
                    segment["text"] = polished_text
                    segment["ai_polished"] = True
                    polished_count += 1
                    
            except Exception as e:
                print(f"⚠️ Error polishing segment {segment['id']}: {e}")
                segment["ai_polished"] = False
        
        print(f"✅ AI polishing complete - {polished_count} segments improved")
        return data

    def review_and_edit(self, data: Dict) -> Dict:
        """Interactive review and editing"""
        transcript = data["transcript"]
        segments = transcript["segments"]
        
        print(f"\n📝 TRANSCRIPT REVIEW")
        print(f"Total segments: {len(segments)}")
        print(f"Average confidence: {transcript['statistics']['average_confidence']:.2f}")
        print(f"Speech delay: {data['speech_delay']:.1f}s")
        
        # Show transcript preview
        print(f"\n📋 TRANSCRIPT PREVIEW:")
        for i, segment in enumerate(segments[:3]):
            status = "🤖" if segment.get("ai_polished") else "📝"
            print(f"  {status} [{segment['start_time']:.1f}s] {segment['text']}")
        
        if len(segments) > 3:
            print(f"  ... and {len(segments) - 3} more segments")
        
        # User choices
        print(f"\n🔧 OPTIONS:")
        print("1. Approve and continue (recommended)")
        print("2. Edit transcript text")
        print("3. Adjust global timing")
        print("4. Show detailed view")
        
        choice = input("\nEnter choice (1-4): ").strip()
        
        if choice == "2":
            data = self.edit_transcript_text(data)
        elif choice == "3":
            data = self.adjust_global_timing(data)
        elif choice == "4":
            self.show_detailed_view(data)
            return self.review_and_edit(data)  # Show menu again
        
        print("✅ Review complete")
        return data

    def edit_transcript_text(self, data: Dict) -> Dict:
        """Edit transcript text"""
        segments = data["transcript"]["segments"]
        
        print(f"\n✏️ TEXT EDITOR")
        for i, segment in enumerate(segments):
            print(f"\nSegment {i+1}/{len(segments)} [{segment['start_time']:.1f}s-{segment['end_time']:.1f}s]")
            print(f"Current: \"{segment['text']}\"")
            
            new_text = input("New text (Enter to keep current): ").strip()
            if new_text:
                segment["user_edited"] = True
                segment["text"] = new_text
                print("✅ Updated")
            
            if i < len(segments) - 1:
                continue_choice = input("Continue editing? (y/n): ").lower().strip()
                if continue_choice != 'y':
                    break
        
        return data

    def adjust_global_timing(self, data: Dict) -> Dict:
        """Adjust timing for all segments"""
        print(f"\n⏰ TIMING ADJUSTMENT")
        print(f"Current speech delay: {data['speech_delay']:.1f}s")
        
        try:
            shift = float(input("Shift all timing by seconds (e.g., +2.0, -1.5): "))
            
            # Adjust all segments
            for segment in data["transcript"]["segments"]:
                segment["start_time"] += shift
                segment["end_time"] += shift
                
                # Adjust word timing
                for word in segment.get("words", []):
                    word["start_time"] += shift
                    word["end_time"] += shift
            
            data["speech_delay"] += shift
            print(f"✅ All timing shifted by {shift:.1f}s")
            
        except ValueError:
            print("❌ Invalid input")
        
        return data

    def show_detailed_view(self, data: Dict):
        """Show detailed transcript view"""
        segments = data["transcript"]["segments"]
        
        print(f"\n📊 DETAILED VIEW")
        for segment in segments:
            status = "🤖" if segment.get("ai_polished") else "📝"
            edited = "✏️" if segment.get("user_edited") else ""
            
            print(f"\n{status}{edited} Segment {segment['id']}")
            print(f"  Time: {segment['start_time']:.1f}s - {segment['end_time']:.1f}s")
            print(f"  Confidence: {segment['confidence']:.2f}")
            print(f"  Text: \"{segment['text']}\"")
            
            if segment.get("original_text"):
                print(f"  Original: \"{segment['original_text']}\"")
        
        input("\nPress Enter to continue...")

    def generate_ai_voice(self, data: Dict, voice_name: str = "aria") -> Dict:
        """Generate AI voice with timing"""
        print(f"🎤 Generating AI voice with {voice_name}...")
        
        # Get voice configuration
        azure_voice = AZURE_VOICE_OPTIONS.get(voice_name, AZURE_SPEECH_TTS_CONFIG["voice_name"])
        self.speech_config.speech_synthesis_voice_name = azure_voice
        
        segments = data["transcript"]["segments"]
        unique_id = data["unique_id"]
        
        # Create timed audio segments
        audio_segments = []
        current_time = 0.0
        
        for segment in segments:
            # Add silence before segment if needed
            silence_needed = segment["start_time"] - current_time
            if silence_needed > 0.1:  # Add silence if gap > 0.1s
                silence_file = f"{TEMP_FOLDER}/silence_{segment['id']}.wav"
                if self.create_silence(silence_needed, silence_file):
                    audio_segments.append(silence_file)
            
            # Generate speech for segment
            speech_file = f"{TEMP_FOLDER}/speech_{segment['id']}.wav"
            if self.generate_speech(segment["text"], speech_file):
                audio_segments.append(speech_file)
                current_time = segment["end_time"]
            else:
                raise ValueError(f"Failed to generate speech for segment {segment['id']}")
        
        # Combine all audio segments
        final_audio_path = f"{TEMP_FOLDER}/ai_voice_{unique_id}.mp3"
        if self.combine_audio(audio_segments, final_audio_path):
            data["ai_audio_path"] = final_audio_path
            
            # Cleanup temporary files
            for temp_file in audio_segments:
                try:
                    os.remove(temp_file)
                except:
                    pass
            
            print("✅ AI voice generated successfully")
            return data
        else:
            raise ValueError("Failed to combine audio segments")

    def create_silence(self, duration: float, output_path: str) -> bool:
        """Create silence audio"""
        try:
            # Create silence using Azure TTS
            silence_ssml = f"""
            <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="en-US">
                <voice name="{self.speech_config.speech_synthesis_voice_name}">
                    <break time="{duration}s"/>
                </voice>
            </speak>
            """
            
            audio_config = speechsdk.audio.AudioOutputConfig(filename=output_path)
            synthesizer = speechsdk.SpeechSynthesizer(
                speech_config=self.speech_config,
                audio_config=audio_config
            )
            
            result = synthesizer.speak_ssml_async(silence_ssml).get()
            return result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted
            
        except Exception:
            return False

    def generate_speech(self, text: str, output_path: str) -> bool:
        """Generate speech for text"""
        try:
            audio_config = speechsdk.audio.AudioOutputConfig(filename=output_path)
            synthesizer = speechsdk.SpeechSynthesizer(
                speech_config=self.speech_config,
                audio_config=audio_config
            )
            
            result = synthesizer.speak_text_async(text).get()
            return result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted
            
        except Exception:
            return False

    def combine_audio(self, audio_files: List[str], output_path: str) -> bool:
        """Combine audio files"""
        try:
            from pydub import AudioSegment
            
            combined = AudioSegment.empty()
            for audio_file in audio_files:
                if os.path.exists(audio_file):
                    segment = AudioSegment.from_file(audio_file)
                    combined += segment
            
            combined.export(output_path, format="mp3")
            return True
            
        except ImportError:
            print("❌ pydub required for audio combination")
            return False
        except Exception:
            return False

    def assemble_final_video(self, data: Dict, original_video_path: str) -> str:
        """Assemble final video with AI audio"""
        print("🎬 Assembling final video...")
        
        ai_audio_path = data["ai_audio_path"]
        unique_id = data["unique_id"]
        
        # Load video and audio
        video = VideoFileClip(original_video_path)
        ai_audio = AudioFileClip(ai_audio_path)
        
        # Handle duration differences
        if ai_audio.duration > video.duration:
            ai_audio = ai_audio.subclip(0, video.duration)
        
        # Create final video
        final_video = video.set_audio(ai_audio)
        
        # Output path
        output_filename = f"ai_voice_video_{unique_id}.mp4"
        output_path = os.path.join(OUTPUT_FOLDER, output_filename)
        
        # Write video
        final_video.write_videofile(
            output_path,
            verbose=False,
            logger=None,
            audio_codec='aac',
            codec='libx264'
        )
        
        # Cleanup
        video.close()
        ai_audio.close()
        final_video.close()
        
        print(f"✅ Final video created: {output_path}")
        return output_path

    def cleanup_temp_files(self, data: Dict):
        """Clean up temporary files"""
        temp_files = [
            data.get("audio_path"),
            data.get("ai_audio_path")
        ]
        
        for temp_file in temp_files:
            if temp_file and os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except:
                    pass

    def process_video(self, video_path: str, voice_choice: str = "aria", auto_approve: bool = False) -> Optional[str]:
        """Complete video processing pipeline"""
        try:
            print(f"🎬 Starting AI voice-over processing for: {os.path.basename(video_path)}")
            
            # Step 1: Extract audio and analyze timing
            data = self.extract_audio_with_timing(video_path)
            
            # Step 2: Transcribe with timing
            data = self.transcribe_with_timing(data)
            
            # Step 3: Polish transcript
            data = self.polish_transcript(data)
            
            # Step 4: User review (unless auto-approve)
            if not auto_approve:
                data = self.review_and_edit(data)
            else:
                print("🤖 Auto-approval enabled - skipping user review")
            
            # Step 5: Generate AI voice
            data = self.generate_ai_voice(data, voice_choice)
            
            # Step 6: Assemble final video
            final_video_path = self.assemble_final_video(data, video_path)
            
            # Cleanup
            self.cleanup_temp_files(data)
            
            print(f"🎉 SUCCESS! AI voice-over completed: {final_video_path}")
            return final_video_path
            
        except Exception as e:
            print(f"❌ ERROR: {str(e)}")
            return None

def main():
    """Main function"""
    if len(sys.argv) < 2:
        print("Usage: python voice_processor.py <video_path> [voice] [--auto]")
        print("Example: python voice_processor.py video.mp4 aria")
        print(f"Available voices: {', '.join(AZURE_VOICE_OPTIONS.keys())}")
        sys.exit(1)
    
    video_path = sys.argv[1]
    voice_choice = sys.argv[2] if len(sys.argv) > 2 else "aria"
    auto_approve = "--auto" in sys.argv
    
    if not os.path.exists(video_path):
        print(f"❌ Video file not found: {video_path}")
        sys.exit(1)
    
    if voice_choice not in AZURE_VOICE_OPTIONS:
        print(f"❌ Invalid voice. Available: {', '.join(AZURE_VOICE_OPTIONS.keys())}")
        sys.exit(1)
    
    processor = VoiceProcessor()
    result = processor.process_video(video_path, voice_choice, auto_approve)
    
    if result:
        print(f"\n🎯 Final video: {result}")
    else:
        print("\n❌ Processing failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
