#!/usr/bin/env python3
"""
Azure AI Voice-Over Processor
"""

import os
import sys
import json
import time
import uuid
from typing import Optional, Dict, List
import azure.cognitiveservices.speech as speechsdk
from moviepy.editor import VideoFileClip, AudioFileClip
from openai import AzureOpenAI
from config import *

# Try to import pydub, use fallback if not available
try:
    from pydub import AudioSegment
    from pydub.silence import detect_leading_silence
    PYDUB_AVAILABLE = True
except ImportError:
    PYDUB_AVAILABLE = False
    print("⚠️ pydub not available - using basic audio processing")

class VoiceProcessor:
    def __init__(self):
        """Initialize the Voice Processor"""
        self.setup_azure()
        create_directories()
        
    def setup_azure(self):
        """Setup Azure services"""
        self.speech_config = speechsdk.SpeechConfig(
            subscription=AZURE_SPEECH_KEY,
            region=AZURE_SPEECH_REGION
        )
        self.speech_config.request_word_level_timestamps()
        self.speech_config.output_format = speechsdk.OutputFormat.Detailed
        self.speech_config.speech_recognition_language = "en-US"

        # Azure OpenAI
        self.openai_client = None
        if ENABLE_AI_POLISHING and AZURE_OPENAI_KEY:
            self.openai_client = AzureOpenAI(
                api_key=AZURE_OPENAI_KEY,
                api_version=AZURE_OPENAI_API_VERSION,
                azure_endpoint=AZURE_OPENAI_ENDPOINT
            )

    def extract_audio_with_timing(self, video_path: str) -> Dict:
        """Extract audio and analyze timing"""
        print("🎵 Extracting audio...")

        unique_id = f"{int(time.time())}_{str(uuid.uuid4())[:8]}"
        audio_path = f"{TEMP_FOLDER}/audio_{unique_id}.wav"

        video = VideoFileClip(video_path)
        if video.audio is None:
            raise ValueError("Video has no audio track")

        # Extract audio for Azure Speech
        audio = video.audio
        audio.write_audiofile(
            audio_path,
            verbose=False,
            logger=None,
            codec='pcm_s16le',
            ffmpeg_params=['-ar', '16000', '-ac', '1']
        )

        video.close()
        audio.close()

        speech_delay = self.detect_speech_delay(audio_path)

        return {
            "unique_id": unique_id,
            "audio_path": audio_path,
            "speech_delay": speech_delay
        }

    def detect_speech_delay(self, audio_path: str) -> float:
        """Detect when speech starts"""
        if PYDUB_AVAILABLE:
            try:
                audio = AudioSegment.from_wav(audio_path)
                silence_ms = detect_leading_silence(audio, silence_threshold=-40.0, chunk_size=10)
                return silence_ms / 1000.0
            except Exception as e:
                print(f"Error detecting speech delay: {e}")

        # Fallback: estimate based on file size
        file_size = os.path.getsize(audio_path)
        return 2.0 if file_size < 5 * 1024 * 1024 else 3.0

    def transcribe_with_timing(self, audio_data: Dict) -> Dict:
        """Transcribe audio with timestamps"""
        print("📝 Transcribing...")

        audio_path = audio_data["audio_path"]
        speech_delay = audio_data["speech_delay"]

        audio_config = speechsdk.audio.AudioConfig(filename=os.path.abspath(audio_path))
        speech_recognizer = speechsdk.SpeechRecognizer(
            speech_config=self.speech_config,
            audio_config=audio_config
        )

        segments = []
        segment_id = 1
        done = False

        def recognized_cb(evt):
            nonlocal segment_id
            if evt.result.reason == speechsdk.ResultReason.RecognizedSpeech:
                try:
                    detailed_result = json.loads(evt.result.json)
                    if 'NBest' in detailed_result and detailed_result['NBest']:
                        best_result = detailed_result['NBest'][0]

                        segment = {
                            "id": segment_id,
                            "text": best_result.get('Display', ''),
                            "start_time": (evt.result.offset / 10000000.0) + speech_delay,
                            "end_time": ((evt.result.offset + evt.result.duration) / 10000000.0) + speech_delay,
                            "confidence": best_result.get('Confidence', 0.0)
                        }

                        segments.append(segment)
                        segment_id += 1
                except Exception as e:
                    print(f"Error processing segment: {e}")

        def stop_cb(evt):
            nonlocal done
            done = True

        speech_recognizer.recognized.connect(recognized_cb)
        speech_recognizer.session_stopped.connect(stop_cb)
        speech_recognizer.canceled.connect(stop_cb)

        speech_recognizer.start_continuous_recognition()

        start_time = time.time()
        while not done and (time.time() - start_time) < MAX_WAIT_TIME:
            time.sleep(0.5)

        speech_recognizer.stop_continuous_recognition()

        if not segments:
            raise ValueError("No speech recognized")

        avg_confidence = sum(seg["confidence"] for seg in segments) / len(segments)

        audio_data["transcript"] = {
            "segments": segments,
            "average_confidence": avg_confidence
        }

        print(f"✅ Transcribed {len(segments)} segments")
        return audio_data

    def polish_transcript(self, data: Dict) -> Dict:
        """Polish transcript using AI"""
        if not self.openai_client:
            print("⚠️ Skipping AI polishing - Azure OpenAI not configured")
            return data

        print("🤖 Polishing transcript...")

        segments = data["transcript"]["segments"]
        polished_count = 0

        for segment in segments:
            original_text = segment["text"]

            try:
                prompt = f"""Fix grammar and spelling: "{original_text}"
Rules: Fix mistakes, improve grammar, remove filler words, keep meaning.
Provide only the improved text:"""

                response = self.openai_client.chat.completions.create(
                    model=AZURE_OPENAI_DEPLOYMENT,
                    messages=[
                        {"role": "system", "content": "You are a transcript editor."},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=200,
                    temperature=0.3
                )

                polished_text = response.choices[0].message.content.strip()

                if polished_text != original_text:
                    segment["original_text"] = original_text
                    segment["text"] = polished_text
                    polished_count += 1

            except Exception as e:
                print(f"Error polishing segment {segment['id']}: {e}")

        if polished_count > 0:
            print(f"✅ Polished {polished_count} segments")
        return data

    def review_and_edit(self, data: Dict) -> Dict:
        """Interactive review and editing"""
        segments = data["transcript"]["segments"]

        print(f"\n📝 TRANSCRIPT REVIEW")
        print(f"Segments: {len(segments)}")
        print(f"Confidence: {data['transcript']['average_confidence']:.2f}")
        print(f"Speech delay: {data['speech_delay']:.1f}s")

        print(f"\n📋 PREVIEW:")
        for segment in segments[:3]:
            print(f"  [{segment['start_time']:.1f}s] {segment['text']}")

        if len(segments) > 3:
            print(f"  ... and {len(segments) - 3} more")

        print(f"\n1. Approve and continue")
        print("2. Edit text")
        print("3. Adjust timing")

        choice = input("\nChoice (1-3): ").strip()

        if choice == "2":
            data = self.edit_transcript_text(data)
        elif choice == "3":
            data = self.adjust_global_timing(data)

        return data

    def edit_transcript_text(self, data: Dict) -> Dict:
        """Edit transcript text"""
        segments = data["transcript"]["segments"]

        print(f"\n✏️ TEXT EDITOR")
        for i, segment in enumerate(segments):
            print(f"\nSegment {i+1}: \"{segment['text']}\"")
            new_text = input("New text (Enter to keep): ").strip()
            if new_text:
                segment["text"] = new_text

            if i < len(segments) - 1:
                if input("Continue? (y/n): ").lower() != 'y':
                    break

        return data

    def adjust_global_timing(self, data: Dict) -> Dict:
        """Adjust timing for all segments"""
        print(f"\n⏰ Current delay: {data['speech_delay']:.1f}s")

        try:
            shift = float(input("Shift timing by seconds: "))

            for segment in data["transcript"]["segments"]:
                segment["start_time"] += shift
                segment["end_time"] += shift

            data["speech_delay"] += shift
            print(f"✅ Shifted by {shift:.1f}s")

        except ValueError:
            print("❌ Invalid input")

        return data

    def generate_ai_voice(self, data: Dict, voice_name: str = "aria") -> Dict:
        """Generate AI voice with timing"""
        print(f"🎤 Generating voice...")

        azure_voice = AZURE_VOICE_OPTIONS.get(voice_name, AZURE_SPEECH_TTS_CONFIG["voice_name"])
        self.speech_config.speech_synthesis_voice_name = azure_voice

        segments = data["transcript"]["segments"]
        unique_id = data["unique_id"]

        audio_segments = []
        current_time = 0.0

        for segment in segments:
            # Add silence if needed
            silence_needed = segment["start_time"] - current_time
            if silence_needed > 0.1:
                silence_file = f"{TEMP_FOLDER}/silence_{segment['id']}.wav"
                if self.create_silence(silence_needed, silence_file):
                    audio_segments.append(silence_file)

            # Generate speech
            speech_file = f"{TEMP_FOLDER}/speech_{segment['id']}.wav"
            if self.generate_speech(segment["text"], speech_file):
                audio_segments.append(speech_file)
                current_time = segment["end_time"]
            else:
                raise ValueError(f"Failed to generate speech for segment {segment['id']}")

        # Combine audio
        final_audio_path = f"{TEMP_FOLDER}/ai_voice_{unique_id}.mp3"
        if self.combine_audio(audio_segments, final_audio_path):
            data["ai_audio_path"] = final_audio_path

            # Cleanup
            for temp_file in audio_segments:
                try:
                    os.remove(temp_file)
                except Exception:
                    pass

            print("✅ Voice generated")
            return data
        else:
            raise ValueError("Failed to combine audio")

    def create_silence(self, duration: float, output_path: str) -> bool:
        """Create silence audio"""
        try:
            silence_ssml = f"""
            <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="en-US">
                <voice name="{self.speech_config.speech_synthesis_voice_name}">
                    <break time="{duration}s"/>
                </voice>
            </speak>
            """

            audio_config = speechsdk.audio.AudioOutputConfig(filename=output_path)
            synthesizer = speechsdk.SpeechSynthesizer(
                speech_config=self.speech_config,
                audio_config=audio_config
            )

            result = synthesizer.speak_ssml_async(silence_ssml).get()
            return result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted
        except Exception as e:
            print(f"Error creating silence: {e}")
            return False

    def generate_speech(self, text: str, output_path: str) -> bool:
        """Generate speech for text"""
        try:
            audio_config = speechsdk.audio.AudioOutputConfig(filename=output_path)
            synthesizer = speechsdk.SpeechSynthesizer(
                speech_config=self.speech_config,
                audio_config=audio_config
            )

            result = synthesizer.speak_text_async(text).get()
            return result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted
        except Exception as e:
            print(f"Error generating speech: {e}")
            return False

    def combine_audio(self, audio_files: List[str], output_path: str) -> bool:
        """Combine audio files"""
        if not PYDUB_AVAILABLE:
            print("❌ pydub required for audio combination")
            return False

        try:
            combined = AudioSegment.empty()
            for audio_file in audio_files:
                if os.path.exists(audio_file):
                    segment = AudioSegment.from_file(audio_file)
                    combined += segment

            combined.export(output_path, format="mp3")
            return True
        except Exception as e:
            print(f"Error combining audio: {e}")
            return False

    def assemble_final_video(self, data: Dict, original_video_path: str) -> str:
        """Assemble final video with AI audio"""
        print("🎬 Assembling video...")

        ai_audio_path = data["ai_audio_path"]
        unique_id = data["unique_id"]

        video = VideoFileClip(original_video_path)
        ai_audio = AudioFileClip(ai_audio_path)

        if ai_audio.duration > video.duration:
            ai_audio = ai_audio.subclip(0, video.duration)

        final_video = video.set_audio(ai_audio)

        output_filename = f"ai_voice_{unique_id}.mp4"
        output_path = os.path.join(OUTPUT_FOLDER, output_filename)

        final_video.write_videofile(
            output_path,
            verbose=False,
            logger=None,
            audio_codec='aac',
            codec='libx264'
        )

        video.close()
        ai_audio.close()
        final_video.close()

        return output_path

    def cleanup_temp_files(self, data: Dict):
        """Clean up temporary files"""
        temp_files = [data.get("audio_path"), data.get("ai_audio_path")]

        for temp_file in temp_files:
            if temp_file and os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except Exception:
                    pass

    def process_video(self, video_path: str, voice_choice: str = "aria", auto_approve: bool = False) -> Optional[str]:
        """Complete video processing pipeline"""
        try:
            print(f"🎬 Processing: {os.path.basename(video_path)}")

            data = self.extract_audio_with_timing(video_path)
            data = self.transcribe_with_timing(data)
            data = self.polish_transcript(data)

            if not auto_approve:
                data = self.review_and_edit(data)

            data = self.generate_ai_voice(data, voice_choice)
            final_video_path = self.assemble_final_video(data, video_path)

            self.cleanup_temp_files(data)

            print(f"🎉 Complete: {final_video_path}")
            return final_video_path

        except Exception as e:
            print(f"❌ Error: {str(e)}")
            return None

def main():
    if len(sys.argv) < 2:
        print("Usage: python voice_processor.py <video_path> [voice] [--auto]")
        print(f"Voices: {', '.join(AZURE_VOICE_OPTIONS.keys())}")
        sys.exit(1)

    video_path = sys.argv[1]
    voice_choice = sys.argv[2] if len(sys.argv) > 2 and sys.argv[2] != "--auto" else "aria"
    auto_approve = "--auto" in sys.argv

    if not os.path.exists(video_path):
        print(f"❌ File not found: {video_path}")
        sys.exit(1)

    if voice_choice not in AZURE_VOICE_OPTIONS:
        print(f"❌ Invalid voice: {voice_choice}")
        sys.exit(1)

    processor = VoiceProcessor()
    result = processor.process_video(video_path, voice_choice, auto_approve)

    if not result:
        sys.exit(1)

if __name__ == "__main__":
    main()
