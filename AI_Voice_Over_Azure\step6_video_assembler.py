#!/usr/bin/env python3
"""
Step 6: Final Video Assembly
Combines original video with AI-generated audio for perfect synchronization
"""

import os
import sys
import json
import logging
import time
import uuid
from typing import Optional, Dict
from moviepy.editor import VideoFileClip, AudioFileClip
from datetime import datetime

# Import configuration
from config import *

class VideoAssembler:
    def __init__(self):
        """Initialize the Video Assembler"""
        self.setup_logging()
        
    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('step6_video_assembly.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)

    def load_voice_data(self, processing_file: str) -> Dict:
        """Load voice generation data from Step 5"""
        try:
            with open(processing_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if data.get("step") != "5_voice_generation":
                raise ValueError("Invalid processing file - not from voice generation step")
            
            self.logger.info(f"Loaded voice generation data: {data['processing_id']}")
            return data
            
        except Exception as e:
            self.logger.error(f"Error loading voice data: {str(e)}")
            raise

    def get_original_video_path(self, voice_data: Dict) -> str:
        """Get the original video file path"""
        try:
            # The original video path should be stored in the initial processing data
            video_metadata = voice_data.get("video_metadata", {})
            original_filename = video_metadata.get("filename", "")
            
            # Look for the video in common locations
            possible_paths = [
                f"input/{original_filename}",
                f"{original_filename}",
                voice_data.get("original_video_path", "")
            ]
            
            for path in possible_paths:
                if path and os.path.exists(path):
                    self.logger.info(f"Found original video: {path}")
                    return path
            
            # If not found, ask user
            print(f"\n⚠️ Original video not found automatically.")
            print(f"Looking for: {original_filename}")
            video_path = input("Please enter the path to the original video file: ").strip()
            
            if os.path.exists(video_path):
                return video_path
            else:
                raise FileNotFoundError(f"Video file not found: {video_path}")
                
        except Exception as e:
            self.logger.error(f"Error finding original video: {str(e)}")
            raise

    def validate_audio_video_compatibility(self, video_path: str, audio_path: str) -> Dict:
        """Validate that audio and video are compatible for assembly"""
        try:
            self.logger.info("Validating audio-video compatibility...")
            
            # Load video and audio
            video = VideoFileClip(video_path)
            audio = AudioFileClip(audio_path)
            
            validation = {
                "video_duration": video.duration,
                "audio_duration": audio.duration,
                "duration_difference": abs(video.duration - audio.duration),
                "video_fps": video.fps,
                "video_size": video.size,
                "audio_fps": audio.fps,
                "compatibility": "good"
            }
            
            # Check duration compatibility
            if validation["duration_difference"] > 2.0:  # More than 2 seconds difference
                validation["compatibility"] = "warning"
                validation["warning"] = f"Duration difference: {validation['duration_difference']:.1f}s"
            
            # Check if video has existing audio
            validation["video_has_audio"] = video.audio is not None
            
            video.close()
            audio.close()
            
            self.logger.info(f"Compatibility check: {validation['compatibility']} "
                           f"(video: {validation['video_duration']:.1f}s, "
                           f"audio: {validation['audio_duration']:.1f}s)")
            
            return validation
            
        except Exception as e:
            self.logger.error(f"Error validating compatibility: {str(e)}")
            raise

    def assemble_video_with_ai_audio(self, video_path: str, ai_audio_path: str, output_path: str) -> bool:
        """Assemble final video with AI audio"""
        try:
            self.logger.info("Starting video assembly with AI audio...")
            
            # Load video and audio
            video = VideoFileClip(video_path)
            ai_audio = AudioFileClip(ai_audio_path)
            
            self.logger.info(f"Video duration: {video.duration:.2f}s, AI audio duration: {ai_audio.duration:.2f}s")
            
            # Handle duration differences
            if ai_audio.duration > video.duration:
                # Trim audio to match video
                ai_audio = ai_audio.subclip(0, video.duration)
                self.logger.info("AI audio trimmed to match video duration")
            elif ai_audio.duration < video.duration:
                # Video is longer than audio - this is usually fine
                self.logger.info("Video is longer than AI audio - will use AI audio as-is")
            
            # Replace video audio with AI audio
            final_video = video.set_audio(ai_audio)
            
            # Write final video
            self.logger.info("Writing final video file...")
            final_video.write_videofile(
                output_path,
                verbose=False,
                logger=None,
                audio_codec='aac',
                codec='libx264',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True
            )
            
            # Cleanup
            video.close()
            ai_audio.close()
            final_video.close()
            
            # Validate output file
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                if file_size > 0:
                    self.logger.info(f"Video assembly completed: {output_path} ({file_size / (1024*1024):.2f} MB)")
                    return True
                else:
                    self.logger.error("Output video file is empty")
                    return False
            else:
                self.logger.error("Output video file was not created")
                return False
                
        except Exception as e:
            self.logger.error(f"Error assembling video: {str(e)}")
            return False

    def upload_to_azure_storage(self, video_path: str, processing_id: str) -> Optional[str]:
        """Upload final video to Azure Blob Storage"""
        try:
            from azure.storage.blob import BlobServiceClient
            
            self.logger.info("Uploading final video to Azure Blob Storage...")
            
            # Create blob service client
            blob_service_client = BlobServiceClient.from_connection_string(
                AZURE_STORAGE_CONNECTION_STRING
            )
            
            # Generate blob name
            timestamp = int(time.time())
            blob_name = f"final_videos/{processing_id}_final_{timestamp}.mp4"
            
            # Upload file
            blob_client = blob_service_client.get_blob_client(
                container=AZURE_CONTAINER_OUTPUT,
                blob=blob_name
            )
            
            with open(video_path, 'rb') as data:
                blob_client.upload_blob(data, overwrite=True)
            
            blob_url = blob_client.url
            self.logger.info(f"Video uploaded to Azure: {blob_url}")
            return blob_url
            
        except Exception as e:
            self.logger.error(f"Error uploading to Azure: {str(e)}")
            return None

    def create_final_summary(self, voice_data: Dict, final_video_path: str, 
                           azure_url: Optional[str], validation: Dict) -> Dict:
        """Create final processing summary"""
        try:
            # Calculate processing statistics
            created_at = datetime.fromisoformat(voice_data.get("created_at", datetime.now().isoformat()))
            completed_at = datetime.now()
            total_processing_time = (completed_at - created_at).total_seconds()
            
            summary = {
                "processing_id": voice_data["processing_id"],
                "original_video": voice_data["video_metadata"]["filename"],
                "final_video_local": final_video_path,
                "final_video_azure": azure_url,
                "processing_summary": {
                    "started_at": voice_data.get("created_at"),
                    "completed_at": completed_at.isoformat(),
                    "total_processing_time_seconds": total_processing_time,
                    "steps_completed": [
                        "1_audio_extraction",
                        "2_transcription", 
                        "3_ai_polishing",
                        "4_user_review",
                        "5_voice_generation",
                        "6_video_assembly"
                    ]
                },
                "quality_metrics": {
                    "transcript_confidence": voice_data["transcript_data"]["statistics"]["average_confidence"],
                    "timing_accuracy": voice_data["voice_generation"]["validation_result"]["timing_accuracy"],
                    "duration_match": validation["compatibility"],
                    "final_video_size_mb": os.path.getsize(final_video_path) / (1024*1024)
                },
                "user_modifications": {
                    "ai_polishing_enabled": voice_data.get("ai_polishing", {}).get("enabled", False),
                    "user_approved": voice_data.get("user_approved", False),
                    "auto_approved": voice_data.get("auto_approved", False)
                }
            }
            
            return summary
            
        except Exception as e:
            self.logger.error(f"Error creating final summary: {str(e)}")
            return {}

    def save_final_data(self, voice_data: Dict, final_summary: Dict) -> str:
        """Save final processing data"""
        try:
            # Update processing data
            voice_data["step"] = "6_video_assembly"
            voice_data["status"] = "completed"
            voice_data["updated_at"] = datetime.now().isoformat()
            voice_data["final_summary"] = final_summary
            
            # Save final data
            processing_id = voice_data["processing_id"]
            output_file = f"{OUTPUT_FOLDER}/{processing_id}_final.json"
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(voice_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Final processing data saved: {output_file}")
            return output_file
            
        except Exception as e:
            self.logger.error(f"Error saving final data: {str(e)}")
            raise

    def process_video_assembly(self, processing_file: str) -> Optional[str]:
        """Main method to assemble final video"""
        try:
            self.logger.info("=== Step 6: Final Video Assembly ===")
            
            # Step 6.1: Load voice generation data
            self.logger.info("📂 Loading voice generation data...")
            voice_data = self.load_voice_data(processing_file)
            
            ai_audio_path = voice_data["voice_generation"]["voice_file"]
            processing_id = voice_data["processing_id"]
            
            # Step 6.2: Find original video
            self.logger.info("🎬 Locating original video...")
            original_video_path = self.get_original_video_path(voice_data)
            
            # Step 6.3: Validate compatibility
            self.logger.info("🔍 Validating audio-video compatibility...")
            validation = self.validate_audio_video_compatibility(original_video_path, ai_audio_path)
            
            if validation["compatibility"] == "warning":
                print(f"\n⚠️ Warning: {validation.get('warning', 'Compatibility issue detected')}")
                continue_choice = input("Continue with assembly? (y/n): ").lower().strip()
                if continue_choice != 'y':
                    self.logger.info("User chose to abort assembly")
                    return None
            
            # Step 6.4: Assemble final video
            self.logger.info("🎬 Assembling final video...")
            
            # Generate output filename
            timestamp = int(time.time())
            output_filename = f"{processing_id}_ai_voice_final_{timestamp}.mp4"
            final_video_path = os.path.join(OUTPUT_FOLDER, output_filename)
            
            if not self.assemble_video_with_ai_audio(original_video_path, ai_audio_path, final_video_path):
                return None
            
            # Step 6.5: Upload to Azure (optional)
            self.logger.info("☁️ Uploading to Azure Blob Storage...")
            azure_url = self.upload_to_azure_storage(final_video_path, processing_id)
            
            # Step 6.6: Create final summary
            self.logger.info("📊 Creating final summary...")
            final_summary = self.create_final_summary(voice_data, final_video_path, azure_url, validation)
            
            # Step 6.7: Save final data
            self.logger.info("💾 Saving final processing data...")
            final_data_file = self.save_final_data(voice_data, final_summary)
            
            # Display results
            self.display_results(final_summary, final_video_path, azure_url)
            
            self.logger.info("✅ Step 6 completed successfully!")
            self.logger.info("🎉 AI Voice-Over process completed!")
            
            return final_data_file
            
        except Exception as e:
            self.logger.error(f"Error in video assembly process: {str(e)}")
            return None

    def display_results(self, final_summary: Dict, final_video_path: str, azure_url: Optional[str]):
        """Display final results to user"""
        print("\n" + "="*80)
        print("🎉 FINAL RESULTS: AI Voice-Over Completed Successfully!")
        print("="*80)
        
        # Processing summary
        processing = final_summary.get("processing_summary", {})
        quality = final_summary.get("quality_metrics", {})
        
        print(f"🎬 Original video: {final_summary.get('original_video', 'Unknown')}")
        print(f"📁 Final video (local): {final_video_path}")
        if azure_url:
            print(f"☁️ Final video (Azure): {azure_url}")
        
        print(f"\n📊 PROCESSING STATISTICS:")
        print(f"⏱️ Total processing time: {processing.get('total_processing_time_seconds', 0):.0f} seconds")
        print(f"⭐ Transcript confidence: {quality.get('transcript_confidence', 0):.2f}")
        print(f"🎯 Timing accuracy: {quality.get('timing_accuracy', 'Unknown')}")
        print(f"📦 Final video size: {quality.get('final_video_size_mb', 0):.1f} MB")
        
        print(f"\n✅ QUALITY ASSURANCE:")
        print(f"🤖 AI polishing: {'✅ Enabled' if final_summary.get('user_modifications', {}).get('ai_polishing_enabled') else '❌ Disabled'}")
        print(f"👤 User review: {'✅ Completed' if final_summary.get('user_modifications', {}).get('user_approved') else '🤖 Auto-approved'}")
        print(f"🎵 Audio-video sync: {quality.get('duration_match', 'Unknown')}")
        
        print(f"\n🎯 SUCCESS! Your AI voice-over video is ready!")
        print("="*80)

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python step6_video_assembler.py <processing_file>")
        print("Example: python step6_video_assembler.py processing/proc_123_step5.json")
        sys.exit(1)
    
    processing_file = sys.argv[1]
    
    if not os.path.exists(processing_file):
        print(f"Error: Processing file not found: {processing_file}")
        sys.exit(1)
    
    assembler = VideoAssembler()
    result = assembler.process_video_assembly(processing_file)
    
    if result:
        print(f"\n🎯 SUCCESS! Final processing data saved to: {result}")
        print("🎉 AI Voice-Over process completed successfully!")
    else:
        print("\n❌ FAILED! Check logs for details")
        sys.exit(1)
