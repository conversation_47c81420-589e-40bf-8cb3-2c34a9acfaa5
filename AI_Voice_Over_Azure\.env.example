# Azure AI Voice-Over Conversion - Environment Variables Template
# Copy this file to .env and fill in your actual Azure credentials

# Azure Speech Services Configuration
AZURE_SPEECH_KEY=your_azure_speech_key_here
AZURE_SPEECH_REGION=eastus

# Azure Storage Configuration
AZURE_STORAGE_ACCOUNT_NAME=your_storage_account_name
AZURE_STORAGE_ACCOUNT_KEY=your_storage_account_key

# Optional: Azure CDN Configuration
AZURE_CDN_ENDPOINT=your_cdn_endpoint.azureedge.net
AZURE_CDN_PROFILE=your_cdn_profile

# Optional: Azure Resource Management
AZURE_SUBSCRIPTION_ID=your_subscription_id
AZURE_RESOURCE_GROUP=your_resource_group

# Processing Configuration (Optional - defaults in config.py)
MAX_FILE_SIZE_MB=200
POLLING_INTERVAL=2
MAX_WAIT_TIME=600
