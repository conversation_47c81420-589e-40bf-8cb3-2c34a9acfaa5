# 🎬 Azure AI Voice-Over Pipeline - Usage Guide

## 📋 **Quick Start**

### **1. Installation**
```bash
# Install dependencies
pip install -r requirements.txt

# Create necessary directories
python config.py
```

### **2. Configuration**
Edit `config.py` with your Azure credentials:
```python
# Azure Speech Services
AZURE_SPEECH_KEY = "your_azure_speech_key"
AZURE_SPEECH_REGION = "eastus"

# Azure Storage
AZURE_STORAGE_ACCOUNT_NAME = "yourstorageaccount"
AZURE_STORAGE_ACCOUNT_KEY = "your_storage_key"

# Azure OpenAI (optional, for AI polishing)
AZURE_OPENAI_KEY = "your_azure_openai_key"
AZURE_OPENAI_ENDPOINT = "https://your-openai.openai.azure.com/"
```

### **3. Basic Usage**
```bash
# Complete automated pipeline
python main_pipeline.py video.mp4 --auto

# Step-by-step with user control
python main_pipeline.py video.mp4 --step-by-step

# Custom voice selection
python main_pipeline.py video.mp4 --voice jenny --auto
```

---

## 🔄 **Pipeline Modes**

### **Mode 1: Fully Automated**
```bash
python main_pipeline.py presentation.mp4 --auto --voice aria
```
- Processes entire video automatically
- Uses AI polishing
- Auto-approves high-confidence transcripts
- Best for: High-quality videos, batch processing

### **Mode 2: Step-by-Step Control**
```bash
python main_pipeline.py presentation.mp4 --step-by-step
```
- Prompts before each step
- Full user control over process
- Can stop/resume at any point
- Best for: Quality control, learning the process

### **Mode 3: User Review (Default)**
```bash
python main_pipeline.py presentation.mp4 --voice jenny
```
- Automatic processing with user review step
- Edit transcript and timing before voice generation
- Balance of automation and control
- Best for: Most use cases

---

## 📝 **Step-by-Step Usage**

### **Step 1: Audio Extraction & Timing Analysis**
```bash
python step1_audio_extractor.py input/video.mp4
```
**What it does:**
- Extracts audio from video
- Detects when speech actually starts
- Analyzes silence periods and gaps
- Creates timing analysis report

**Output:** `processing/proc_123_step1.json`

### **Step 2: Transcription with Real Timing**
```bash
python step2_transcriber.py processing/proc_123_step1.json
```
**What it does:**
- Transcribes audio using Azure Speech Services
- Gets word-level timestamps
- Adjusts timing for real video synchronization
- Identifies low-confidence words

**Output:** `processing/proc_123_step2.json`

### **Step 3: AI-Powered Transcript Polishing**
```bash
python step3_ai_polisher.py processing/proc_123_step2.json
```
**What it does:**
- Uses Azure OpenAI to fix grammar/spelling
- Improves readability while preserving meaning
- Maintains all timestamps
- Creates change report

**Output:** `processing/proc_123_step3.json`

### **Step 4: User Review & Content Editing**
```bash
python step4_user_editor.py processing/proc_123_step3.json
```
**What it does:**
- Interactive transcript editor
- Edit text content and timing
- Preview audio segments
- Global timing adjustments

**Output:** `processing/proc_123_step4.json`

### **Step 5: AI Voice Generation with Timing**
```bash
python step5_voice_generator.py processing/proc_123_step4.json aria
```
**What it does:**
- Generates AI voice using Azure Neural Voices
- Respects exact timing from transcript
- Adds silence for delays and gaps
- Validates timing accuracy

**Output:** `processing/proc_123_step5.json`

### **Step 6: Final Video Assembly**
```bash
python step6_video_assembler.py processing/proc_123_step5.json
```
**What it does:**
- Combines original video with AI audio
- Perfect synchronization
- Uploads to Azure Blob Storage
- Creates final summary

**Output:** `output/proc_123_final.json` + final video

---

## 🎤 **Voice Options**

Available Azure Neural Voices:
- **aria**: `en-US-AriaNeural` - Natural, friendly female voice
- **jenny**: `en-US-JennyNeural` - Professional female voice  
- **guy**: `en-US-GuyNeural` - Casual male voice
- **davis**: `en-US-DavisNeural` - Professional male voice
- **jane**: `en-US-JaneNeural` - Warm female voice

```bash
# Use specific voice
python main_pipeline.py video.mp4 --voice davis --auto
```

---

## ✏️ **User Editing Features**

### **Text Editing**
- Fix spelling mistakes
- Improve grammar
- Add or remove content
- Correct AI polishing errors

### **Timing Control**
- Adjust when segments play
- Change speech speed (duration)
- Add pauses between segments
- Global timing shifts

### **Quality Control**
- Preview audio segments
- See confidence scores
- Review AI changes
- Validate timing accuracy

---

## 🔄 **Resume & Recovery**

### **Resume from Specific Step**
```bash
# Resume from step 4 (user review)
python main_pipeline.py --resume processing/proc_123_step3.json --from-step 4

# Resume from step 5 (voice generation)
python main_pipeline.py --resume processing/proc_123_step4.json --from-step 5
```

### **Run Individual Steps**
```bash
# Run only transcription
python main_pipeline.py --step2 processing/proc_123_step1.json

# Run only voice generation
python main_pipeline.py --step5 processing/proc_123_step4.json
```

---

## 📊 **File Structure**

```
AI_Voice_Over_Azure/
├── input/                    # Place your videos here
├── processing/               # Intermediate processing files
│   ├── proc_123_step1.json  # Audio extraction results
│   ├── proc_123_step2.json  # Transcription results
│   ├── proc_123_step3.json  # AI polishing results
│   ├── proc_123_step4.json  # User review results
│   └── proc_123_step5.json  # Voice generation results
├── temp/                     # Temporary audio files
├── output/                   # Final videos and summaries
│   ├── proc_123_final.json  # Complete processing summary
│   └── proc_123_final.mp4   # Final AI voice-over video
└── transcripts/              # Transcript backups
```

---

## 🎯 **Best Practices**

### **For High Quality Results:**
1. Use clear, high-quality source videos
2. Ensure good audio quality (minimal background noise)
3. Review and edit transcripts for accuracy
4. Test different voices to find the best match
5. Validate timing before final generation

### **For Batch Processing:**
1. Use `--auto` mode for consistent videos
2. Enable auto-approval for high-confidence transcripts
3. Process similar videos together
4. Monitor Azure costs and usage

### **For Troubleshooting:**
1. Check log files for detailed error information
2. Validate Azure credentials and permissions
3. Ensure sufficient storage space
4. Test with shorter videos first

---

## 💰 **Cost Optimization**

### **Typical Costs (10-minute video):**
- Azure Speech STT: ~$0.17
- Azure Speech TTS: ~$0.02
- Azure OpenAI polishing: ~$0.01
- Azure Storage: ~$0.01
- **Total: ~$0.21 per video**

### **Cost Reduction Tips:**
- Use auto-approval for high-quality videos
- Batch process similar content
- Monitor and optimize storage usage
- Use appropriate Azure regions

---

## 🔧 **Configuration Options**

### **Processing Settings**
```python
# Enable/disable AI polishing
ENABLE_AI_POLISHING = True

# Auto-approve high confidence transcripts
AUTO_APPROVE_HIGH_CONFIDENCE = True
HIGH_CONFIDENCE_THRESHOLD = 0.95

# Require user approval
REQUIRE_USER_APPROVAL = True
```

### **Voice Settings**
```python
# Default voice
DEFAULT_VOICE = "aria"

# TTS configuration
AZURE_SPEECH_TTS_CONFIG = {
    "voice_name": "en-US-AriaNeural",
    "language": "en-US",
    "output_format": "audio-24khz-48kbitrate-mono-mp3"
}
```

---

## 🆘 **Troubleshooting**

### **Common Issues:**

**1. Azure Authentication Error**
- Verify Azure Speech key and region
- Check Azure Storage connection string
- Ensure proper permissions

**2. Transcription Failed**
- Check audio quality
- Verify file format support
- Ensure sufficient Azure quota

**3. Voice Generation Error**
- Validate transcript approval
- Check voice name spelling
- Verify text length limits

**4. Video Assembly Failed**
- Ensure original video is accessible
- Check video format compatibility
- Verify sufficient disk space

### **Getting Help:**
- Check log files for detailed errors
- Review Azure service status
- Validate configuration settings
- Test with sample videos

---

**🎉 You're ready to create amazing AI voice-over videos with perfect timing control!**
