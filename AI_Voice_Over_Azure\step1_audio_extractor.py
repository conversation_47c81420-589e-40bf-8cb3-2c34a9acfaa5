#!/usr/bin/env python3
"""
Step 1: Enhanced Audio Extraction with Timing Detection
Extracts audio from video and analyzes timing patterns for perfect synchronization
"""

import os
import sys
import time
import json
import uuid
import logging
from typing import Optional, Dict, List
from moviepy.editor import VideoFileClip
from datetime import datetime
import numpy as np

# Import configuration
from config import *

class AudioExtractor:
    def __init__(self):
        """Initialize the Audio Extractor"""
        self.setup_logging()
        create_directories()
        
    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('step1_audio_extraction.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)

    def extract_video_metadata(self, video_path: str) -> Dict:
        """Extract comprehensive video metadata"""
        try:
            self.logger.info(f"Analyzing video metadata: {video_path}")
            
            video = VideoFileClip(video_path)
            
            metadata = {
                "filename": os.path.basename(video_path),
                "duration": video.duration,
                "fps": video.fps,
                "size": video.size,
                "has_audio": video.audio is not None,
                "audio_fps": video.audio.fps if video.audio else None,
                "audio_duration": video.audio.duration if video.audio else None,
                "file_size_mb": os.path.getsize(video_path) / (1024 * 1024),
                "extraction_time": datetime.now().isoformat()
            }
            
            video.close()
            
            self.logger.info(f"Video metadata extracted: {metadata['duration']:.2f}s, {metadata['fps']}fps")
            return metadata
            
        except Exception as e:
            self.logger.error(f"Error extracting video metadata: {str(e)}")
            raise

    def detect_speech_timing(self, audio_path: str) -> Dict:
        """Detect when speech actually starts and analyze timing patterns"""
        try:
            self.logger.info("Analyzing speech timing patterns...")
            
            # Try to use pydub for audio analysis
            try:
                from pydub import AudioSegment
                from pydub.silence import detect_leading_silence, detect_silence
                
                # Load audio
                audio = AudioSegment.from_wav(audio_path)
                
                # Detect leading silence (speech start delay)
                leading_silence_ms = detect_leading_silence(audio, silence_threshold=-40.0, chunk_size=10)
                speech_start_delay = leading_silence_ms / 1000.0
                
                # Detect all silence periods
                silence_periods = detect_silence(audio, min_silence_len=500, silence_thresh=-40.0)
                silence_periods_seconds = [
                    {"start": start/1000.0, "end": end/1000.0, "duration": (end-start)/1000.0}
                    for start, end in silence_periods
                ]
                
                # Calculate speech end (last non-silent part)
                audio_duration = len(audio) / 1000.0
                trailing_silence_ms = detect_leading_silence(audio.reverse(), silence_threshold=-40.0, chunk_size=10)
                speech_end = audio_duration - (trailing_silence_ms / 1000.0)
                
                timing_analysis = {
                    "speech_start_delay": speech_start_delay,
                    "speech_end": speech_end,
                    "total_speech_duration": speech_end - speech_start_delay,
                    "silence_periods": silence_periods_seconds,
                    "audio_duration": audio_duration,
                    "speech_percentage": ((speech_end - speech_start_delay) / audio_duration) * 100
                }
                
                self.logger.info(f"Speech timing detected: starts at {speech_start_delay:.2f}s, ends at {speech_end:.2f}s")
                return timing_analysis
                
            except ImportError:
                self.logger.warning("pydub not available, using basic timing detection")
                return self.basic_timing_detection(audio_path)
                
        except Exception as e:
            self.logger.error(f"Error detecting speech timing: {str(e)}")
            return self.basic_timing_detection(audio_path)

    def basic_timing_detection(self, audio_path: str) -> Dict:
        """Basic timing detection without pydub"""
        try:
            # Simple analysis using file size and duration estimation
            file_size = os.path.getsize(audio_path)
            
            # Estimate based on file characteristics (basic heuristic)
            estimated_delay = 0.0
            if file_size < 1024 * 1024:  # Less than 1MB
                estimated_delay = 1.0
            elif file_size < 5 * 1024 * 1024:  # Less than 5MB
                estimated_delay = 2.0
            else:
                estimated_delay = 3.0
            
            return {
                "speech_start_delay": estimated_delay,
                "speech_end": 60.0,  # Default estimate
                "total_speech_duration": 57.0,
                "silence_periods": [],
                "audio_duration": 60.0,
                "speech_percentage": 95.0,
                "detection_method": "basic_estimation"
            }
            
        except Exception as e:
            self.logger.error(f"Error in basic timing detection: {str(e)}")
            return {
                "speech_start_delay": 0.0,
                "speech_end": 60.0,
                "total_speech_duration": 60.0,
                "silence_periods": [],
                "audio_duration": 60.0,
                "speech_percentage": 100.0,
                "detection_method": "fallback"
            }

    def extract_audio_optimized(self, video_path: str, audio_path: str) -> bool:
        """Extract audio optimized for Azure Speech Services"""
        try:
            self.logger.info(f"Extracting audio from: {video_path}")
            
            video = VideoFileClip(video_path)
            
            if video.audio is None:
                raise ValueError("Video file has no audio track")
            
            # Extract audio with Azure-optimized parameters
            audio = video.audio
            audio.write_audiofile(
                audio_path,
                verbose=False,
                logger=None,
                codec='pcm_s16le',  # 16-bit PCM for Azure Speech
                ffmpeg_params=['-ar', '16000', '-ac', '1']  # 16kHz mono for Azure Speech
            )
            
            video.close()
            audio.close()
            
            # Validate extracted audio
            if not os.path.exists(audio_path):
                raise ValueError("Audio extraction failed - file not created")
            
            audio_size = os.path.getsize(audio_path)
            if audio_size == 0:
                raise ValueError("Audio extraction failed - empty file")
            
            self.logger.info(f"Audio extracted successfully: {audio_size / (1024*1024):.2f} MB")
            return True
            
        except Exception as e:
            self.logger.error(f"Error extracting audio: {str(e)}")
            return False

    def save_processing_data(self, video_metadata: Dict, timing_analysis: Dict, 
                           audio_path: str, output_dir: str) -> str:
        """Save all processing data for next steps"""
        try:
            # Generate unique processing ID
            processing_id = f"proc_{int(time.time())}_{str(uuid.uuid4())[:8]}"
            
            # Create processing data structure
            processing_data = {
                "processing_id": processing_id,
                "step": "1_audio_extraction",
                "status": "completed",
                "created_at": datetime.now().isoformat(),
                "video_metadata": video_metadata,
                "timing_analysis": timing_analysis,
                "files": {
                    "audio_file": audio_path,
                    "processing_data": f"{output_dir}/{processing_id}_step1.json"
                },
                "next_step": "2_transcription"
            }
            
            # Save processing data
            output_file = f"{output_dir}/{processing_id}_step1.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(processing_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Processing data saved: {output_file}")
            return output_file
            
        except Exception as e:
            self.logger.error(f"Error saving processing data: {str(e)}")
            raise

    def process_video(self, video_path: str) -> Optional[str]:
        """Main method to process video and extract audio with timing analysis"""
        try:
            if not os.path.exists(video_path):
                raise ValueError(f"Video file not found: {video_path}")
            
            # Generate unique filenames
            timestamp = int(time.time())
            unique_id = str(uuid.uuid4())[:8]
            
            audio_filename = f"audio_{timestamp}_{unique_id}.wav"
            audio_path = os.path.join(TEMP_FOLDER, audio_filename)
            
            self.logger.info("=== Step 1: Audio Extraction with Timing Analysis ===")
            
            # Step 1.1: Extract video metadata
            self.logger.info("📊 Extracting video metadata...")
            video_metadata = self.extract_video_metadata(video_path)
            
            # Step 1.2: Extract audio optimized for Azure Speech
            self.logger.info("🎵 Extracting audio...")
            if not self.extract_audio_optimized(video_path, audio_path):
                return None
            
            # Step 1.3: Analyze speech timing patterns
            self.logger.info("⏰ Analyzing speech timing...")
            timing_analysis = self.detect_speech_timing(audio_path)
            
            # Step 1.4: Save processing data for next steps
            self.logger.info("💾 Saving processing data...")
            processing_file = self.save_processing_data(
                video_metadata, timing_analysis, audio_path, PROCESSING_FOLDER
            )
            
            # Display results
            self.display_results(video_metadata, timing_analysis)
            
            self.logger.info("✅ Step 1 completed successfully!")
            self.logger.info(f"📁 Processing data: {processing_file}")
            self.logger.info("📝 Next step: Run transcription with timing")
            
            return processing_file
            
        except Exception as e:
            self.logger.error(f"Error in process_video: {str(e)}")
            return None

    def display_results(self, video_metadata: Dict, timing_analysis: Dict):
        """Display processing results to user"""
        print("\n" + "="*60)
        print("🎬 STEP 1 RESULTS: Audio Extraction & Timing Analysis")
        print("="*60)
        print(f"📹 Video: {video_metadata['filename']}")
        print(f"⏱️  Duration: {video_metadata['duration']:.2f} seconds")
        print(f"📊 File size: {video_metadata['file_size_mb']:.2f} MB")
        print(f"🎵 Audio: {'✅ Present' if video_metadata['has_audio'] else '❌ Missing'}")
        print()
        print("⏰ TIMING ANALYSIS:")
        print(f"🗣️  Speech starts at: {timing_analysis['speech_start_delay']:.2f} seconds")
        print(f"🏁 Speech ends at: {timing_analysis['speech_end']:.2f} seconds")
        print(f"📈 Speech percentage: {timing_analysis['speech_percentage']:.1f}%")
        
        if timing_analysis['silence_periods']:
            print(f"🔇 Silence periods: {len(timing_analysis['silence_periods'])} detected")
            for i, period in enumerate(timing_analysis['silence_periods'][:3]):  # Show first 3
                print(f"   • Gap {i+1}: {period['start']:.1f}s - {period['end']:.1f}s ({period['duration']:.1f}s)")
        
        print("\n✅ Ready for Step 2: Transcription with timing")
        print("="*60)

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python step1_audio_extractor.py <video_path>")
        print("Example: python step1_audio_extractor.py input/presentation.mp4")
        sys.exit(1)
    
    video_path = sys.argv[1]
    
    extractor = AudioExtractor()
    result = extractor.process_video(video_path)
    
    if result:
        print(f"\n🎯 SUCCESS! Processing data saved to: {result}")
        print("📝 Next: python step2_transcriber.py <processing_file>")
    else:
        print("\n❌ FAILED! Check logs for details")
        sys.exit(1)
