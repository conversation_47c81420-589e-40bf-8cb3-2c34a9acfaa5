#!/usr/bin/env python3
"""
Step 2: Enhanced Transcription with Real Timing
Transcribes audio using Azure Speech Services with word-level timestamps adjusted for real video timing
"""

import os
import sys
import time
import json
import logging
from typing import Optional, Dict, List
import azure.cognitiveservices.speech as speechsdk
from datetime import datetime

# Import configuration
from config import *

class TimedTranscriber:
    def __init__(self):
        """Initialize the Timed Transcriber"""
        self.setup_logging()
        self.setup_azure_speech()
        
    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('step2_transcription.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_azure_speech(self):
        """Setup Azure Speech Services client"""
        try:
            if not AZURE_SPEECH_KEY or AZURE_SPEECH_KEY == "your_azure_speech_key_here":
                raise ValueError("Azure Speech key not configured")

            self.speech_config = speechsdk.SpeechConfig(
                subscription=AZURE_SPEECH_KEY,
                region=AZURE_SPEECH_REGION
            )
            
            # Enable detailed results for word-level timestamps
            self.speech_config.request_word_level_timestamps()
            self.speech_config.output_format = speechsdk.OutputFormat.Detailed
            self.speech_config.speech_recognition_language = "en-US"
            
            self.logger.info("Azure Speech Services initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Error setting up Azure Speech: {str(e)}")
            raise

    def load_processing_data(self, processing_file: str) -> Dict:
        """Load processing data from Step 1"""
        try:
            with open(processing_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.info(f"Loaded processing data: {data['processing_id']}")
            return data
            
        except Exception as e:
            self.logger.error(f"Error loading processing data: {str(e)}")
            raise

    def transcribe_with_azure_speech(self, audio_path: str) -> Optional[Dict]:
        """Transcribe audio using Azure Speech Services with detailed timestamps"""
        try:
            self.logger.info("Starting Azure Speech-to-Text transcription...")
            
            # Create audio configuration
            audio_config = speechsdk.audio.AudioConfig(filename=os.path.abspath(audio_path))
            
            # Create speech recognizer
            speech_recognizer = speechsdk.SpeechRecognizer(
                speech_config=self.speech_config,
                audio_config=audio_config
            )
            
            # Initialize transcript data
            transcript_data = {
                "transcription_metadata": {
                    "service": "Azure Speech Services",
                    "language": "en-US",
                    "transcription_time": datetime.now().isoformat(),
                    "audio_file": audio_path
                },
                "segments": [],
                "raw_results": [],
                "statistics": {
                    "total_segments": 0,
                    "total_words": 0,
                    "average_confidence": 0.0,
                    "low_confidence_words": []
                }
            }
            
            # Use continuous recognition for better accuracy
            done = False
            segment_id = 1
            all_confidence_scores = []
            
            def recognized_cb(evt):
                nonlocal segment_id
                if evt.result.reason == speechsdk.ResultReason.RecognizedSpeech:
                    try:
                        # Parse detailed JSON results
                        detailed_result = evt.result.json
                        result_data = json.loads(detailed_result)
                        
                        # Store raw result for debugging
                        transcript_data["raw_results"].append(result_data)
                        
                        if 'NBest' in result_data and result_data['NBest']:
                            best_result = result_data['NBest'][0]
                            
                            # Create segment with basic timing (will be adjusted later)
                            segment = {
                                "id": segment_id,
                                "text": best_result.get('Display', ''),
                                "start_time": evt.result.offset / 10000000.0,  # Convert to seconds
                                "duration": evt.result.duration / 10000000.0,
                                "confidence": best_result.get('Confidence', 0.0),
                                "words": [],
                                "azure_raw_timing": {
                                    "offset": evt.result.offset,
                                    "duration": evt.result.duration
                                }
                            }
                            
                            # Extract word-level details
                            if 'Words' in best_result:
                                for word_info in best_result['Words']:
                                    word_confidence = word_info.get('Confidence', 0.0)
                                    word_data = {
                                        "word": word_info.get('Word', ''),
                                        "start_time": word_info.get('Offset', 0) / 10000000.0,
                                        "duration": word_info.get('Duration', 0) / 10000000.0,
                                        "confidence": word_confidence
                                    }
                                    word_data["end_time"] = word_data["start_time"] + word_data["duration"]
                                    segment["words"].append(word_data)
                                    
                                    # Track low confidence words
                                    if word_confidence < 0.8:
                                        transcript_data["statistics"]["low_confidence_words"].append({
                                            "word": word_data["word"],
                                            "confidence": word_confidence,
                                            "segment_id": segment_id
                                        })
                            
                            segment["end_time"] = segment["start_time"] + segment["duration"]
                            transcript_data["segments"].append(segment)
                            all_confidence_scores.append(segment["confidence"])
                            
                            self.logger.info(f"Segment {segment_id}: {segment['text'][:50]}... (confidence: {segment['confidence']:.2f})")
                            segment_id += 1
                            
                    except Exception as e:
                        self.logger.error(f"Error processing recognition result: {str(e)}")

            def stop_cb(evt):
                nonlocal done
                done = True

            # Connect callbacks
            speech_recognizer.recognized.connect(recognized_cb)
            speech_recognizer.session_stopped.connect(stop_cb)
            speech_recognizer.canceled.connect(stop_cb)

            # Start continuous recognition
            speech_recognizer.start_continuous_recognition()

            # Wait for completion
            start_time = time.time()
            while not done and (time.time() - start_time) < MAX_WAIT_TIME:
                time.sleep(0.5)

            speech_recognizer.stop_continuous_recognition()

            # Calculate statistics
            if transcript_data["segments"]:
                transcript_data["statistics"]["total_segments"] = len(transcript_data["segments"])
                transcript_data["statistics"]["total_words"] = sum([len(seg["words"]) for seg in transcript_data["segments"]])
                transcript_data["statistics"]["average_confidence"] = sum(all_confidence_scores) / len(all_confidence_scores)
                
                self.logger.info(f"Transcription completed: {transcript_data['statistics']['total_words']} words, "
                               f"{transcript_data['statistics']['total_segments']} segments, "
                               f"avg confidence: {transcript_data['statistics']['average_confidence']:.2f}")
                return transcript_data
            else:
                self.logger.error("No speech recognized in audio")
                return None

        except Exception as e:
            self.logger.error(f"Error in Azure transcription: {str(e)}")
            return None

    def adjust_timing_for_video(self, transcript_data: Dict, timing_analysis: Dict) -> Dict:
        """Adjust all timestamps to match real video timing"""
        try:
            self.logger.info("Adjusting timestamps for real video timing...")
            
            speech_delay = timing_analysis.get('speech_start_delay', 0.0)
            
            # Adjust all segment timestamps
            for segment in transcript_data["segments"]:
                # Adjust segment timing
                segment["original_start_time"] = segment["start_time"]
                segment["original_end_time"] = segment["end_time"]
                segment["start_time"] += speech_delay
                segment["end_time"] += speech_delay
                
                # Adjust word-level timing
                for word in segment["words"]:
                    word["original_start_time"] = word["start_time"]
                    word["original_end_time"] = word["end_time"]
                    word["start_time"] += speech_delay
                    word["end_time"] += speech_delay
            
            # Add timing adjustment metadata
            transcript_data["timing_adjustment"] = {
                "speech_delay_applied": speech_delay,
                "adjustment_time": datetime.now().isoformat(),
                "original_timing_preserved": True
            }
            
            self.logger.info(f"Timing adjusted: added {speech_delay:.2f}s delay to all timestamps")
            return transcript_data
            
        except Exception as e:
            self.logger.error(f"Error adjusting timing: {str(e)}")
            return transcript_data

    def save_transcript_data(self, processing_data: Dict, transcript_data: Dict) -> str:
        """Save transcript data for next step"""
        try:
            # Update processing data
            processing_data["step"] = "2_transcription"
            processing_data["status"] = "completed"
            processing_data["updated_at"] = datetime.now().isoformat()
            processing_data["transcript_data"] = transcript_data
            processing_data["next_step"] = "3_ai_polishing"
            
            # Save updated processing data
            processing_id = processing_data["processing_id"]
            output_file = f"{PROCESSING_FOLDER}/{processing_id}_step2.json"
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(processing_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Transcript data saved: {output_file}")
            return output_file
            
        except Exception as e:
            self.logger.error(f"Error saving transcript data: {str(e)}")
            raise

    def process_transcription(self, processing_file: str) -> Optional[str]:
        """Main method to process transcription with timing adjustment"""
        try:
            self.logger.info("=== Step 2: Transcription with Real Timing ===")
            
            # Step 2.1: Load processing data from Step 1
            self.logger.info("📂 Loading processing data...")
            processing_data = self.load_processing_data(processing_file)
            
            audio_path = processing_data["files"]["audio_file"]
            timing_analysis = processing_data["timing_analysis"]
            
            # Step 2.2: Transcribe with Azure Speech Services
            self.logger.info("🎤 Transcribing with Azure Speech Services...")
            transcript_data = self.transcribe_with_azure_speech(audio_path)
            if not transcript_data:
                return None
            
            # Step 2.3: Adjust timing for real video synchronization
            self.logger.info("⏰ Adjusting timing for video synchronization...")
            transcript_data = self.adjust_timing_for_video(transcript_data, timing_analysis)
            
            # Step 2.4: Save transcript data
            self.logger.info("💾 Saving transcript data...")
            output_file = self.save_transcript_data(processing_data, transcript_data)
            
            # Display results
            self.display_results(transcript_data, timing_analysis)
            
            self.logger.info("✅ Step 2 completed successfully!")
            self.logger.info(f"📁 Transcript data: {output_file}")
            self.logger.info("🤖 Next step: Run AI polishing")
            
            return output_file
            
        except Exception as e:
            self.logger.error(f"Error in process_transcription: {str(e)}")
            return None

    def display_results(self, transcript_data: Dict, timing_analysis: Dict):
        """Display transcription results to user"""
        stats = transcript_data["statistics"]
        timing_adj = transcript_data.get("timing_adjustment", {})
        
        print("\n" + "="*60)
        print("📝 STEP 2 RESULTS: Transcription with Real Timing")
        print("="*60)
        print(f"🎤 Service: Azure Speech Services")
        print(f"📊 Total words: {stats['total_words']}")
        print(f"📋 Total segments: {stats['total_segments']}")
        print(f"⭐ Average confidence: {stats['average_confidence']:.2f}")
        print(f"⚠️  Low confidence words: {len(stats['low_confidence_words'])}")
        print()
        print("⏰ TIMING ADJUSTMENT:")
        print(f"🕐 Speech delay applied: {timing_adj.get('speech_delay_applied', 0):.2f} seconds")
        print(f"🎯 All timestamps adjusted for video sync")
        
        if stats['low_confidence_words']:
            print(f"\n⚠️  WORDS NEEDING REVIEW:")
            for word_info in stats['low_confidence_words'][:5]:  # Show first 5
                print(f"   • '{word_info['word']}' (confidence: {word_info['confidence']:.2f})")
        
        # Show first few segments as preview
        print(f"\n📝 TRANSCRIPT PREVIEW:")
        for segment in transcript_data["segments"][:3]:
            print(f"   [{segment['start_time']:.1f}s-{segment['end_time']:.1f}s] {segment['text']}")
        
        print("\n✅ Ready for Step 3: AI Polishing")
        print("="*60)

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python step2_transcriber.py <processing_file>")
        print("Example: python step2_transcriber.py processing/proc_123_step1.json")
        sys.exit(1)
    
    processing_file = sys.argv[1]
    
    if not os.path.exists(processing_file):
        print(f"Error: Processing file not found: {processing_file}")
        sys.exit(1)
    
    transcriber = TimedTranscriber()
    result = transcriber.process_transcription(processing_file)
    
    if result:
        print(f"\n🎯 SUCCESS! Transcript data saved to: {result}")
        print("🤖 Next: python step3_ai_polisher.py <transcript_file>")
    else:
        print("\n❌ FAILED! Check logs for details")
        sys.exit(1)
