#!/usr/bin/env python3
"""
Main Pipeline Orchestrator
Coordinates the complete AI voice-over process with user control points
"""

import os
import sys
import argparse
import logging
from typing import Optional
from datetime import datetime

# Import all step modules
from step1_audio_extractor import AudioExtractor
from step2_transcriber import TimedTranscriber
from step3_ai_polisher import AIPolisher
from step4_user_editor import UserEditor
from step5_voice_generator import TimedVoiceGenerator
from step6_video_assembler import VideoAssembler

# Import configuration
from config import *

class VoiceOverPipeline:
    def __init__(self):
        """Initialize the complete pipeline"""
        self.setup_logging()
        self.audio_extractor = AudioExtractor()
        self.transcriber = TimedTranscriber()
        self.ai_polisher = AIPolisher()
        self.user_editor = UserEditor()
        self.voice_generator = TimedVoiceGenerator()
        self.video_assembler = VideoAssembler()
        
    def setup_logging(self):
        """Setup pipeline logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('main_pipeline.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)

    def print_banner(self):
        """Print pipeline banner"""
        print("\n" + "="*80)
        print("🎬 AZURE AI VOICE-OVER PIPELINE")
        print("Complete Multi-Step Processing with User Control")
        print("="*80)

    def print_step_separator(self, step_num: int, step_name: str):
        """Print step separator"""
        print(f"\n{'='*20} STEP {step_num}: {step_name.upper()} {'='*20}")

    def run_complete_pipeline(self, video_path: str, voice_choice: str = "aria", 
                            auto_approve: bool = False) -> Optional[str]:
        """Run the complete pipeline from start to finish"""
        try:
            self.print_banner()
            self.logger.info(f"Starting complete pipeline for: {video_path}")
            
            # Step 1: Audio Extraction with Timing Analysis
            self.print_step_separator(1, "Audio Extraction & Timing Analysis")
            step1_result = self.audio_extractor.process_video(video_path)
            if not step1_result:
                self.logger.error("Step 1 failed: Audio extraction")
                return None
            
            # Step 2: Transcription with Real Timing
            self.print_step_separator(2, "Transcription with Real Timing")
            step2_result = self.transcriber.process_transcription(step1_result)
            if not step2_result:
                self.logger.error("Step 2 failed: Transcription")
                return None
            
            # Step 3: AI Polishing
            self.print_step_separator(3, "AI-Powered Transcript Polishing")
            step3_result = self.ai_polisher.process_polishing(step2_result)
            if not step3_result:
                self.logger.error("Step 3 failed: AI polishing")
                return None
            
            # Step 4: User Review (unless auto-approve is enabled)
            self.print_step_separator(4, "User Review & Content Editing")
            if auto_approve:
                print("🤖 Auto-approval enabled - skipping user review")
                step4_result = step3_result
            else:
                step4_result = self.user_editor.process_user_review(step3_result)
                if not step4_result:
                    self.logger.error("Step 4 failed: User review")
                    return None
            
            # Step 5: AI Voice Generation
            self.print_step_separator(5, "AI Voice Generation with Timing")
            step5_result = self.voice_generator.process_voice_generation(step4_result, voice_choice)
            if not step5_result:
                self.logger.error("Step 5 failed: Voice generation")
                return None
            
            # Step 6: Final Video Assembly
            self.print_step_separator(6, "Final Video Assembly")
            final_result = self.video_assembler.process_video_assembly(step5_result)
            if not final_result:
                self.logger.error("Step 6 failed: Video assembly")
                return None
            
            self.print_completion_summary(final_result)
            return final_result
            
        except Exception as e:
            self.logger.error(f"Pipeline error: {str(e)}")
            return None

    def run_step_by_step(self, video_path: str, start_step: int = 1) -> Optional[str]:
        """Run pipeline step by step with user confirmation"""
        try:
            self.print_banner()
            print("🔄 Step-by-step mode: You'll be prompted before each step")
            
            current_file = None
            
            # Step 1: Audio Extraction
            if start_step <= 1:
                if self.confirm_step(1, "Audio Extraction & Timing Analysis"):
                    self.print_step_separator(1, "Audio Extraction & Timing Analysis")
                    current_file = self.audio_extractor.process_video(video_path)
                    if not current_file:
                        return None
                else:
                    return None
            
            # Step 2: Transcription
            if start_step <= 2 and current_file:
                if self.confirm_step(2, "Transcription with Real Timing"):
                    self.print_step_separator(2, "Transcription with Real Timing")
                    current_file = self.transcriber.process_transcription(current_file)
                    if not current_file:
                        return None
                else:
                    return current_file
            
            # Step 3: AI Polishing
            if start_step <= 3 and current_file:
                if self.confirm_step(3, "AI-Powered Transcript Polishing"):
                    self.print_step_separator(3, "AI-Powered Transcript Polishing")
                    current_file = self.ai_polisher.process_polishing(current_file)
                    if not current_file:
                        return None
                else:
                    return current_file
            
            # Step 4: User Review
            if start_step <= 4 and current_file:
                if self.confirm_step(4, "User Review & Content Editing"):
                    self.print_step_separator(4, "User Review & Content Editing")
                    current_file = self.user_editor.process_user_review(current_file)
                    if not current_file:
                        return None
                else:
                    return current_file
            
            # Step 5: Voice Generation
            if start_step <= 5 and current_file:
                voice_choice = input("Enter voice choice (aria/jenny/guy/davis/jane) [aria]: ").strip() or "aria"
                if self.confirm_step(5, f"AI Voice Generation with {voice_choice}"):
                    self.print_step_separator(5, "AI Voice Generation with Timing")
                    current_file = self.voice_generator.process_voice_generation(current_file, voice_choice)
                    if not current_file:
                        return None
                else:
                    return current_file
            
            # Step 6: Video Assembly
            if start_step <= 6 and current_file:
                if self.confirm_step(6, "Final Video Assembly"):
                    self.print_step_separator(6, "Final Video Assembly")
                    current_file = self.video_assembler.process_video_assembly(current_file)
                    if not current_file:
                        return None
                else:
                    return current_file
            
            if current_file:
                self.print_completion_summary(current_file)
            
            return current_file
            
        except Exception as e:
            self.logger.error(f"Step-by-step pipeline error: {str(e)}")
            return None

    def confirm_step(self, step_num: int, step_name: str) -> bool:
        """Ask user confirmation before running a step"""
        print(f"\n🔄 Ready to run Step {step_num}: {step_name}")
        choice = input("Continue? (y/n/q): ").lower().strip()
        
        if choice == 'q':
            print("Pipeline stopped by user")
            sys.exit(0)
        
        return choice == 'y'

    def print_completion_summary(self, final_result_file: str):
        """Print completion summary"""
        print("\n" + "="*80)
        print("🎉 PIPELINE COMPLETED SUCCESSFULLY!")
        print("="*80)
        print(f"📁 Final result file: {final_result_file}")
        print("✅ All steps completed successfully")
        print("🎬 Your AI voice-over video is ready!")
        print("="*80)

    def resume_from_step(self, processing_file: str, step_num: int, voice_choice: str = "aria") -> Optional[str]:
        """Resume pipeline from a specific step"""
        try:
            self.print_banner()
            print(f"🔄 Resuming from Step {step_num}")
            
            current_file = processing_file
            
            if step_num <= 3:
                self.print_step_separator(3, "AI-Powered Transcript Polishing")
                current_file = self.ai_polisher.process_polishing(current_file)
                if not current_file:
                    return None
            
            if step_num <= 4:
                self.print_step_separator(4, "User Review & Content Editing")
                current_file = self.user_editor.process_user_review(current_file)
                if not current_file:
                    return None
            
            if step_num <= 5:
                self.print_step_separator(5, "AI Voice Generation with Timing")
                current_file = self.voice_generator.process_voice_generation(current_file, voice_choice)
                if not current_file:
                    return None
            
            if step_num <= 6:
                self.print_step_separator(6, "Final Video Assembly")
                current_file = self.video_assembler.process_video_assembly(current_file)
                if not current_file:
                    return None
            
            self.print_completion_summary(current_file)
            return current_file
            
        except Exception as e:
            self.logger.error(f"Resume pipeline error: {str(e)}")
            return None

def main():
    """Main function with command line interface"""
    parser = argparse.ArgumentParser(
        description="Azure AI Voice-Over Pipeline - Complete multi-step processing",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Complete automated pipeline
  python main_pipeline.py video.mp4 --auto

  # Step-by-step with user control
  python main_pipeline.py video.mp4 --step-by-step

  # Resume from specific step
  python main_pipeline.py --resume processing/proc_123_step3.json --from-step 4

  # Custom voice selection
  python main_pipeline.py video.mp4 --voice jenny --auto
        """
    )
    
    # Main arguments
    parser.add_argument("video_path", nargs='?', help="Path to video file")
    parser.add_argument("--voice", choices=list(AZURE_VOICE_OPTIONS.keys()), 
                       default="aria", help="Azure neural voice to use")
    
    # Processing modes
    parser.add_argument("--auto", action="store_true", 
                       help="Run complete pipeline automatically")
    parser.add_argument("--step-by-step", action="store_true",
                       help="Run pipeline step by step with confirmations")
    
    # Resume functionality
    parser.add_argument("--resume", help="Resume from processing file")
    parser.add_argument("--from-step", type=int, choices=[2,3,4,5,6],
                       help="Resume from specific step number")
    
    # Individual steps
    parser.add_argument("--step1", help="Run only step 1 on video file")
    parser.add_argument("--step2", help="Run only step 2 on processing file")
    parser.add_argument("--step3", help="Run only step 3 on processing file")
    parser.add_argument("--step4", help="Run only step 4 on processing file")
    parser.add_argument("--step5", help="Run only step 5 on processing file")
    parser.add_argument("--step6", help="Run only step 6 on processing file")
    
    args = parser.parse_args()
    
    # Initialize pipeline
    pipeline = VoiceOverPipeline()
    
    # Handle individual steps
    if args.step1:
        result = pipeline.audio_extractor.process_video(args.step1)
        print(f"Step 1 result: {result}")
        return
    elif args.step2:
        result = pipeline.transcriber.process_transcription(args.step2)
        print(f"Step 2 result: {result}")
        return
    elif args.step3:
        result = pipeline.ai_polisher.process_polishing(args.step3)
        print(f"Step 3 result: {result}")
        return
    elif args.step4:
        result = pipeline.user_editor.process_user_review(args.step4)
        print(f"Step 4 result: {result}")
        return
    elif args.step5:
        result = pipeline.voice_generator.process_voice_generation(args.step5, args.voice)
        print(f"Step 5 result: {result}")
        return
    elif args.step6:
        result = pipeline.video_assembler.process_video_assembly(args.step6)
        print(f"Step 6 result: {result}")
        return
    
    # Handle resume functionality
    if args.resume:
        if not args.from_step:
            print("Error: --from-step required when using --resume")
            sys.exit(1)
        
        result = pipeline.resume_from_step(args.resume, args.from_step, args.voice)
        if result:
            print(f"\n🎯 Pipeline resumed successfully!")
        else:
            print(f"\n❌ Pipeline resume failed!")
        return
    
    # Main pipeline modes
    if not args.video_path:
        print("Error: Video path required for main pipeline")
        parser.print_help()
        sys.exit(1)
    
    if not os.path.exists(args.video_path):
        print(f"Error: Video file not found: {args.video_path}")
        sys.exit(1)
    
    # Run pipeline
    if args.auto:
        result = pipeline.run_complete_pipeline(args.video_path, args.voice, auto_approve=True)
    elif args.step_by_step:
        result = pipeline.run_step_by_step(args.video_path)
    else:
        # Default: complete pipeline with user review
        result = pipeline.run_complete_pipeline(args.video_path, args.voice, auto_approve=False)
    
    if result:
        print(f"\n🎯 SUCCESS! Pipeline completed successfully!")
    else:
        print(f"\n❌ FAILED! Pipeline did not complete successfully!")
        sys.exit(1)

if __name__ == "__main__":
    main()
