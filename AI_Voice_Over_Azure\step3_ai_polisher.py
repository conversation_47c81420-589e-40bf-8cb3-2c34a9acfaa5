#!/usr/bin/env python3
"""
Step 3: AI-Powered Transcript Polishing
Uses Azure OpenAI to improve transcript quality while preserving timing
"""

import os
import sys
import json
import logging
from typing import Optional, Dict, List
from datetime import datetime
import copy

# Import configuration
from config import *

class AIPolisher:
    def __init__(self):
        """Initialize the AI Polisher"""
        self.setup_logging()
        self.setup_azure_openai()
        
    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('step3_ai_polishing.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_azure_openai(self):
        """Setup Azure OpenAI client"""
        try:
            if not ENABLE_AI_POLISHING:
                self.logger.info("AI polishing disabled in config")
                self.openai_client = None
                return
                
            # Try to import and setup Azure OpenAI
            try:
                from openai import AzureOpenAI
                
                if (not AZURE_OPENAI_KEY or 
                    AZURE_OPENAI_KEY == "your_azure_openai_key_here" or
                    not AZURE_OPENAI_ENDPOINT):
                    self.logger.warning("Azure OpenAI not configured, skipping AI polishing")
                    self.openai_client = None
                    return
                
                self.openai_client = AzureOpenAI(
                    api_key=AZURE_OPENAI_KEY,
                    api_version=AZURE_OPENAI_API_VERSION,
                    azure_endpoint=AZURE_OPENAI_ENDPOINT
                )
                
                self.logger.info("Azure OpenAI client initialized successfully")
                
            except ImportError:
                self.logger.warning("Azure OpenAI library not available, skipping AI polishing")
                self.openai_client = None
                
        except Exception as e:
            self.logger.error(f"Error setting up Azure OpenAI: {str(e)}")
            self.openai_client = None

    def load_transcript_data(self, processing_file: str) -> Dict:
        """Load transcript data from Step 2"""
        try:
            with open(processing_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.info(f"Loaded transcript data: {data['processing_id']}")
            return data
            
        except Exception as e:
            self.logger.error(f"Error loading transcript data: {str(e)}")
            raise

    def polish_segment_text(self, segment_text: str, context: str = "") -> Dict:
        """Polish a single segment using Azure OpenAI"""
        try:
            if not self.openai_client:
                return {
                    "polished_text": segment_text,
                    "changes_made": [],
                    "confidence": 1.0,
                    "method": "no_polishing"
                }
            
            # Create polishing prompt
            prompt = f"""
You are an expert transcript editor. Your task is to improve the following transcript segment while preserving its original meaning and natural speech patterns.

Rules:
1. Fix obvious spelling mistakes
2. Improve grammar naturally (don't make it too formal)
3. Remove filler words (um, uh, like) only if excessive
4. Keep technical terms unchanged
5. Preserve the speaker's natural style
6. Don't add new information
7. Keep the same approximate length

Context (previous text): {context}

Original text: "{segment_text}"

Provide only the improved text, nothing else.
"""

            response = self.openai_client.chat.completions.create(
                model=AZURE_OPENAI_DEPLOYMENT,
                messages=[
                    {"role": "system", "content": "You are a professional transcript editor focused on clarity and accuracy."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.3
            )
            
            polished_text = response.choices[0].message.content.strip()
            
            # Analyze changes made
            changes = self.analyze_changes(segment_text, polished_text)
            
            return {
                "polished_text": polished_text,
                "changes_made": changes,
                "confidence": 0.9,  # High confidence for AI polishing
                "method": "azure_openai"
            }
            
        except Exception as e:
            self.logger.error(f"Error polishing segment: {str(e)}")
            return {
                "polished_text": segment_text,
                "changes_made": [],
                "confidence": 1.0,
                "method": "error_fallback"
            }

    def analyze_changes(self, original: str, polished: str) -> List[Dict]:
        """Analyze what changes were made during polishing"""
        changes = []
        
        # Simple change detection (can be enhanced with difflib)
        if original != polished:
            # Basic change detection
            original_words = original.split()
            polished_words = polished.split()
            
            if len(original_words) != len(polished_words):
                changes.append({
                    "type": "length_change",
                    "description": f"Word count changed from {len(original_words)} to {len(polished_words)}"
                })
            
            # Check for common improvements
            if "um" in original.lower() and "um" not in polished.lower():
                changes.append({
                    "type": "filler_removal",
                    "description": "Removed filler words"
                })
            
            if original.lower() != polished.lower():
                changes.append({
                    "type": "text_improvement",
                    "description": "Grammar/spelling improvements"
                })
        
        return changes

    def polish_transcript(self, transcript_data: Dict) -> Dict:
        """Polish entire transcript while preserving timing"""
        try:
            self.logger.info("Starting AI transcript polishing...")
            
            # Create a copy to preserve original
            polished_data = copy.deepcopy(transcript_data)
            
            # Track polishing statistics
            polishing_stats = {
                "segments_processed": 0,
                "segments_changed": 0,
                "total_changes": 0,
                "polishing_time": datetime.now().isoformat()
            }
            
            # Polish each segment
            context = ""
            for i, segment in enumerate(polished_data["transcript_data"]["segments"]):
                self.logger.info(f"Polishing segment {i+1}/{len(polished_data['transcript_data']['segments'])}")
                
                original_text = segment["text"]
                
                # Polish the segment
                polish_result = self.polish_segment_text(original_text, context)
                
                # Update segment with polished text
                segment["original_text"] = original_text
                segment["text"] = polish_result["polished_text"]
                segment["ai_polished"] = True
                segment["polishing_info"] = {
                    "changes_made": polish_result["changes_made"],
                    "confidence": polish_result["confidence"],
                    "method": polish_result["method"]
                }
                
                # Update statistics
                polishing_stats["segments_processed"] += 1
                if original_text != polish_result["polished_text"]:
                    polishing_stats["segments_changed"] += 1
                    polishing_stats["total_changes"] += len(polish_result["changes_made"])
                
                # Update context for next segment
                context = polish_result["polished_text"][-100:]  # Last 100 chars as context
            
            # Add polishing metadata
            polished_data["ai_polishing"] = {
                "enabled": True,
                "service": "Azure OpenAI",
                "model": AZURE_OPENAI_DEPLOYMENT,
                "statistics": polishing_stats
            }
            
            self.logger.info(f"AI polishing completed: {polishing_stats['segments_changed']}/{polishing_stats['segments_processed']} segments improved")
            return polished_data
            
        except Exception as e:
            self.logger.error(f"Error in AI polishing: {str(e)}")
            # Return original data if polishing fails
            transcript_data["ai_polishing"] = {
                "enabled": False,
                "error": str(e),
                "fallback": True
            }
            return transcript_data

    def identify_review_priorities(self, polished_data: Dict) -> List[Dict]:
        """Identify segments that need user review"""
        review_priorities = []
        
        transcript_data = polished_data["transcript_data"]
        
        # Check low confidence words
        for word_info in transcript_data["statistics"]["low_confidence_words"]:
            review_priorities.append({
                "type": "low_confidence",
                "priority": "high",
                "segment_id": word_info["segment_id"],
                "word": word_info["word"],
                "confidence": word_info["confidence"],
                "reason": f"Low confidence word: {word_info['confidence']:.2f}"
            })
        
        # Check significant AI changes
        for segment in transcript_data["segments"]:
            if segment.get("ai_polished") and segment.get("polishing_info"):
                changes = segment["polishing_info"]["changes_made"]
                if len(changes) > 2:  # Many changes made
                    review_priorities.append({
                        "type": "significant_changes",
                        "priority": "medium",
                        "segment_id": segment["id"],
                        "changes": len(changes),
                        "reason": f"AI made {len(changes)} changes to this segment"
                    })
        
        # Sort by priority
        priority_order = {"high": 1, "medium": 2, "low": 3}
        review_priorities.sort(key=lambda x: priority_order[x["priority"]])
        
        return review_priorities

    def save_polished_data(self, polished_data: Dict) -> str:
        """Save polished transcript data"""
        try:
            # Update processing data
            polished_data["step"] = "3_ai_polishing"
            polished_data["status"] = "completed"
            polished_data["updated_at"] = datetime.now().isoformat()
            polished_data["next_step"] = "4_user_review"
            
            # Add review priorities
            polished_data["review_priorities"] = self.identify_review_priorities(polished_data)
            
            # Save polished data
            processing_id = polished_data["processing_id"]
            output_file = f"{PROCESSING_FOLDER}/{processing_id}_step3.json"
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(polished_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Polished data saved: {output_file}")
            return output_file
            
        except Exception as e:
            self.logger.error(f"Error saving polished data: {str(e)}")
            raise

    def process_polishing(self, processing_file: str) -> Optional[str]:
        """Main method to process AI polishing"""
        try:
            self.logger.info("=== Step 3: AI-Powered Transcript Polishing ===")
            
            # Step 3.1: Load transcript data from Step 2
            self.logger.info("📂 Loading transcript data...")
            transcript_data = self.load_transcript_data(processing_file)
            
            # Step 3.2: Polish transcript with AI
            self.logger.info("🤖 Polishing transcript with Azure OpenAI...")
            polished_data = self.polish_transcript(transcript_data)
            
            # Step 3.3: Save polished data
            self.logger.info("💾 Saving polished data...")
            output_file = self.save_polished_data(polished_data)
            
            # Display results
            self.display_results(polished_data)
            
            self.logger.info("✅ Step 3 completed successfully!")
            self.logger.info(f"📁 Polished data: {output_file}")
            self.logger.info("✏️ Next step: User review and editing")
            
            return output_file
            
        except Exception as e:
            self.logger.error(f"Error in process_polishing: {str(e)}")
            return None

    def display_results(self, polished_data: Dict):
        """Display polishing results to user"""
        ai_polishing = polished_data.get("ai_polishing", {})
        review_priorities = polished_data.get("review_priorities", [])
        
        print("\n" + "="*60)
        print("🤖 STEP 3 RESULTS: AI-Powered Transcript Polishing")
        print("="*60)
        
        if ai_polishing.get("enabled"):
            stats = ai_polishing["statistics"]
            print(f"🎯 Service: {ai_polishing['service']}")
            print(f"📊 Segments processed: {stats['segments_processed']}")
            print(f"✏️ Segments improved: {stats['segments_changed']}")
            print(f"🔧 Total changes: {stats['total_changes']}")
        else:
            print("⚠️ AI polishing was skipped or failed")
        
        print(f"\n📋 REVIEW PRIORITIES:")
        if review_priorities:
            high_priority = [r for r in review_priorities if r["priority"] == "high"]
            medium_priority = [r for r in review_priorities if r["priority"] == "medium"]
            
            print(f"🔴 High priority: {len(high_priority)} items")
            print(f"🟡 Medium priority: {len(medium_priority)} items")
            
            # Show top priorities
            for item in review_priorities[:5]:
                priority_icon = "🔴" if item["priority"] == "high" else "🟡"
                print(f"   {priority_icon} Segment {item.get('segment_id', 'N/A')}: {item['reason']}")
        else:
            print("✅ No issues detected - transcript looks good!")
        
        print("\n✅ Ready for Step 4: User Review & Editing")
        print("="*60)

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python step3_ai_polisher.py <processing_file>")
        print("Example: python step3_ai_polisher.py processing/proc_123_step2.json")
        sys.exit(1)
    
    processing_file = sys.argv[1]
    
    if not os.path.exists(processing_file):
        print(f"Error: Processing file not found: {processing_file}")
        sys.exit(1)
    
    polisher = AIPolisher()
    result = polisher.process_polishing(processing_file)
    
    if result:
        print(f"\n🎯 SUCCESS! Polished data saved to: {result}")
        print("✏️ Next: python step4_user_editor.py <polished_file>")
    else:
        print("\n❌ FAILED! Check logs for details")
        sys.exit(1)
